function [wind_force, wind_torque, wind_info] = WindDisturbanceModel(t, pos, vel, att, wind_params)
% WindDisturbanceModel - 四旋翼无人机风扰动模型
%
% 功能：生成真实的风扰动力和力矩，包括多种风扰类型
%
% 输入：
%   t          - 当前时间 (s)
%   pos        - 位置 [x; y; z] (m)
%   vel        - 速度 [vx; vy; vz] (m/s)
%   att        - 姿态角 [phi; theta; psi] (rad)
%   wind_params - 风扰参数结构体
%
% 输出：
%   wind_force - 风扰动力 [Fx; Fy; Fz] (N)
%   wind_torque - 风扰动力矩 [Mx; My; Mz] (N·m)
%   wind_info  - 风扰信息结构体
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0

%% 参数检查和默认值设置
if nargin < 5 || isempty(wind_params)
    wind_params = GetDefaultWindParams();
end

% 初始化输出
wind_force = zeros(3, 1);
wind_torque = zeros(3, 1);
wind_info = struct();

%% 1. 常值风 (Constant Wind)
if wind_params.enable_constant_wind
    % 常值风速度 (m/s)
    V_constant = wind_params.constant_wind_velocity; % [Vx; Vy; Vz]
    
    % 计算相对风速
    V_rel_constant = V_constant - vel;
    V_rel_mag = norm(V_rel_constant);
    
    if V_rel_mag > 0.01  % 避免除零
        % 风阻力系数
        Cd = wind_params.drag_coefficient;
        rho = wind_params.air_density;
        A = wind_params.reference_area;
        
        % 风阻力 F = 0.5 * rho * Cd * A * V_rel^2 * (V_rel/|V_rel|)
        F_constant = -0.5 * rho * Cd * A * V_rel_mag * V_rel_constant;
        wind_force = wind_force + F_constant;
        
        % 风扰力矩（由于机体不对称）
        M_constant = wind_params.torque_coefficient * cross(wind_params.cp_offset, F_constant);
        wind_torque = wind_torque + M_constant;
    end
    
    wind_info.constant_wind = V_constant;
end

%% 2. 阵风 (Gust Wind)
if wind_params.enable_gust_wind
    % 阵风模型：1-cosine gust
    gust_start = wind_params.gust_start_time;
    gust_duration = wind_params.gust_duration;
    gust_amplitude = wind_params.gust_amplitude;
    
    if t >= gust_start && t <= gust_start + gust_duration
        % 1-cosine阵风形状
        tau = (t - gust_start) / gust_duration;
        gust_factor = 0.5 * (1 - cos(pi * tau));
        
        % 阵风速度
        V_gust = gust_amplitude * gust_factor * wind_params.gust_direction;
        
        % 计算阵风力
        V_rel_gust = V_gust - vel;
        V_rel_mag = norm(V_rel_gust);
        
        if V_rel_mag > 0.01
            Cd = wind_params.drag_coefficient;
            rho = wind_params.air_density;
            A = wind_params.reference_area;
            
            F_gust = -0.5 * rho * Cd * A * V_rel_mag * V_rel_gust;
            wind_force = wind_force + F_gust;
            
            % 阵风力矩
            M_gust = wind_params.torque_coefficient * cross(wind_params.cp_offset, F_gust);
            wind_torque = wind_torque + M_gust;
        end
        
        wind_info.gust_wind = V_gust;
        wind_info.gust_factor = gust_factor;
    else
        wind_info.gust_wind = zeros(3, 1);
        wind_info.gust_factor = 0;
    end
end

%% 3. 湍流 (Turbulence)
if wind_params.enable_turbulence
    % Dryden湍流模型
    sigma_u = wind_params.turbulence_intensity(1);  % x方向湍流强度
    sigma_v = wind_params.turbulence_intensity(2);  % y方向湍流强度
    sigma_w = wind_params.turbulence_intensity(3);  % z方向湍流强度
    
    L_u = wind_params.turbulence_scale(1);  % x方向湍流尺度
    L_v = wind_params.turbulence_scale(2);  % y方向湍流尺度
    L_w = wind_params.turbulence_scale(3);  % z方向湍流尺度
    
    % 生成有色噪声（简化的Dryden模型）
    dt = 0.01;  % 假设仿真步长
    
    % 一阶滤波器系数
    a_u = exp(-dt * abs(vel(1)) / L_u);
    a_v = exp(-dt * abs(vel(2)) / L_v);
    a_w = exp(-dt * abs(vel(3)) / L_w);
    
    % 白噪声输入
    white_noise = randn(3, 1);
    
    % 湍流速度（简化模型）
    V_turb = [
        sigma_u * sqrt(2 * abs(vel(1)) / (pi * L_u)) * white_noise(1);
        sigma_v * sqrt(2 * abs(vel(2)) / (pi * L_v)) * white_noise(2);
        sigma_w * sqrt(2 * abs(vel(3)) / (pi * L_w)) * white_noise(3)
    ];
    
    % 计算湍流力
    V_rel_turb = V_turb - vel;
    V_rel_mag = norm(V_rel_turb);
    
    if V_rel_mag > 0.01
        Cd = wind_params.drag_coefficient;
        rho = wind_params.air_density;
        A = wind_params.reference_area;
        
        F_turb = -0.5 * rho * Cd * A * V_rel_mag * V_rel_turb;
        wind_force = wind_force + F_turb;
        
        % 湍流力矩
        M_turb = wind_params.torque_coefficient * cross(wind_params.cp_offset, F_turb);
        wind_torque = wind_torque + M_turb;
    end
    
    wind_info.turbulence = V_turb;
end

%% 4. 风切变 (Wind Shear)
if wind_params.enable_wind_shear
    % 高度相关的风切变
    h_ref = wind_params.shear_reference_height;  % 参考高度
    alpha = wind_params.shear_exponent;          % 风切变指数
    V_ref = wind_params.shear_reference_velocity; % 参考高度风速
    
    % 当前高度
    h = pos(3);
    
    if h > 0.1  % 避免地面附近的数值问题
        % 风切变速度
        V_shear = V_ref * (h / h_ref)^alpha;
        V_shear_vec = V_shear * wind_params.shear_direction;
        
        % 计算风切变力
        V_rel_shear = V_shear_vec - vel;
        V_rel_mag = norm(V_rel_shear);
        
        if V_rel_mag > 0.01
            Cd = wind_params.drag_coefficient;
            rho = wind_params.air_density;
            A = wind_params.reference_area;
            
            F_shear = -0.5 * rho * Cd * A * V_rel_mag * V_rel_shear;
            wind_force = wind_force + F_shear;
            
            % 风切变力矩
            M_shear = wind_params.torque_coefficient * cross(wind_params.cp_offset, F_shear);
            wind_torque = wind_torque + M_shear;
        end
        
        wind_info.wind_shear = V_shear_vec;
    else
        wind_info.wind_shear = zeros(3, 1);
    end
end

%% 5. 旋转风场 (Vortex Wind)
if wind_params.enable_vortex_wind
    % 旋转风场中心
    vortex_center = wind_params.vortex_center;
    vortex_strength = wind_params.vortex_strength;
    vortex_radius = wind_params.vortex_radius;
    
    % 相对位置
    r_vec = pos - vortex_center;
    r_mag = norm(r_vec(1:2));  % 只考虑水平距离
    
    if r_mag > 0.1 && r_mag < vortex_radius
        % 切向速度
        V_tangential = vortex_strength / (2 * pi * r_mag);
        
        % 切向方向（垂直于径向）
        tangential_dir = [-r_vec(2); r_vec(1); 0] / r_mag;
        
        % 旋转风速度
        V_vortex = V_tangential * tangential_dir;
        
        % 计算旋转风力
        V_rel_vortex = V_vortex - vel;
        V_rel_mag = norm(V_rel_vortex);
        
        if V_rel_mag > 0.01
            Cd = wind_params.drag_coefficient;
            rho = wind_params.air_density;
            A = wind_params.reference_area;
            
            F_vortex = -0.5 * rho * Cd * A * V_rel_mag * V_rel_vortex;
            wind_force = wind_force + F_vortex;
            
            % 旋转风力矩
            M_vortex = wind_params.torque_coefficient * cross(wind_params.cp_offset, F_vortex);
            wind_torque = wind_torque + M_vortex;
        end
        
        wind_info.vortex_wind = V_vortex;
    else
        wind_info.vortex_wind = zeros(3, 1);
    end
end

%% 输出总风扰信息
wind_info.total_force = wind_force;
wind_info.total_torque = wind_torque;
wind_info.time = t;
wind_info.position = pos;

end

%% 默认风扰参数函数
function params = GetDefaultWindParams()
% 获取默认风扰参数

params = struct();

% 物理参数
params.air_density = 1.225;           % 空气密度 (kg/m³)
params.drag_coefficient = 1.2;       % 阻力系数
params.reference_area = 0.1;          % 参考面积 (m²)
params.torque_coefficient = 0.1;      % 力矩系数
params.cp_offset = [0; 0; 0.05];      % 压心偏移 (m)

% 常值风参数
params.enable_constant_wind = true;
params.constant_wind_velocity = [2; 1; 0.5];  % 常值风速 (m/s)

% 阵风参数
params.enable_gust_wind = true;
params.gust_start_time = 10;          % 阵风开始时间 (s)
params.gust_duration = 3;             % 阵风持续时间 (s)
params.gust_amplitude = 5;            % 阵风幅值 (m/s)
params.gust_direction = [1; 0; 0];    % 阵风方向

% 湍流参数
params.enable_turbulence = true;
params.turbulence_intensity = [0.5; 0.5; 0.3];  % 湍流强度 (m/s)
params.turbulence_scale = [50; 50; 20];         % 湍流尺度 (m)

% 风切变参数
params.enable_wind_shear = true;
params.shear_reference_height = 10;   % 参考高度 (m)
params.shear_reference_velocity = 3;  % 参考风速 (m/s)
params.shear_exponent = 0.2;          % 风切变指数
params.shear_direction = [1; 0; 0];   % 风切变方向

% 旋转风场参数
params.enable_vortex_wind = false;    % 默认关闭
params.vortex_center = [0; 0; 5];     % 旋转中心 (m)
params.vortex_strength = 10;          % 旋转强度 (m²/s)
params.vortex_radius = 20;            % 影响半径 (m)

end
