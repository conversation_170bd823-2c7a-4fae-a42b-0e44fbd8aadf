function RobustRectangularFormationDemo()
% RobustRectangularFormationDemo - 带干扰处理的矩形编队控制演示
%
% 功能：演示基于分布式观测器的四旋翼矩形编队控制系统，
%       集成风扰动模型、干扰观测器和鲁棒控制器
%
% 特点：
%   1. 完整的风扰动建模（常值风、阵风、湍流、风切变）
%   2. 实时干扰观测器（基于扩展状态观测器）
%   3. 鲁棒控制器（PD+干扰补偿+滑模控制）
%   4. 领导者-跟随者矩形编队
%   5. 分布式观测器状态估计
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0

clear; clc; close all;

fprintf('=== 带干扰处理的四旋翼矩形编队控制系统 ===\n');
fprintf('正在初始化系统参数...\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 30;             % 仿真时间 (s)
N_steps = round(T_sim / dt);

% 无人机数量
N_uav = 5;              % 1个领导者 + 4个跟随者
leader_idx = 1;         % 领导者索引

%% 系统参数
% 四旋翼物理参数
quad_params = struct();
quad_params.mass = 1.2;                    % 质量 (kg)
quad_params.gravity = 9.81;                % 重力加速度 (m/s²)
quad_params.inertia = diag([0.0347, 0.0347, 0.0617]);  % 转动惯量 (kg·m²)

% 编队参数
formation_size = 2.0;   % 编队尺寸 (m)
formation_height = 5.0; % 编队高度 (m)

% 矩形编队偏移（相对于领导者）
formation_offset = zeros(3, N_uav-1);
formation_offset(:, 1) = [formation_size; formation_size; 0];      % 跟随者1: 右前
formation_offset(:, 2) = [-formation_size; formation_size; 0];     % 跟随者2: 左前
formation_offset(:, 3) = [-formation_size; -formation_size; 0];    % 跟随者3: 左后
formation_offset(:, 4) = [formation_size; -formation_size; 0];     % 跟随者4: 右后

%% 风扰动参数设置
wind_params = struct();
wind_params.enable_constant_wind = true;
wind_params.constant_wind_velocity = [3; 2; 0.5];  % 常值风速 (m/s)

wind_params.enable_gust_wind = true;
wind_params.gust_start_time = 10;          % 阵风开始时间 (s)
wind_params.gust_duration = 4;             % 阵风持续时间 (s)
wind_params.gust_amplitude = 6;            % 阵风幅值 (m/s)
wind_params.gust_direction = [1; 0.5; 0];  % 阵风方向

wind_params.enable_turbulence = true;
wind_params.turbulence_intensity = [0.8; 0.6; 0.4];  % 湍流强度 (m/s)

wind_params.enable_wind_shear = true;
wind_params.shear_reference_height = 10;   % 参考高度 (m)
wind_params.shear_reference_velocity = 4;  % 参考风速 (m/s)

fprintf('风扰动参数设置完成\n');

%% 控制器参数设置
controller_params = struct();
controller_params.mass = quad_params.mass;
controller_params.gravity = quad_params.gravity;
controller_params.inertia = quad_params.inertia;

% 基础控制增益
controller_params.k_pos = 2.5;            % 位置控制增益
controller_params.k_vel = 2.0;            % 速度控制增益
controller_params.k_att = 3.0;            % 姿态控制增益
controller_params.k_omega = 1.5;          % 角速度控制增益

% 启用干扰处理功能
controller_params.enable_disturbance_compensation = true;
controller_params.enable_adaptive_gains = true;
controller_params.enable_sliding_mode = true;

fprintf('控制器参数设置完成\n');

%% 观测器参数设置
observer_params = GetDefaultObserverParams();
observer_params.mass = quad_params.mass;
observer_params.gravity = quad_params.gravity;
observer_params.inertia = quad_params.inertia;

fprintf('观测器参数设置完成\n');

%% 状态变量初始化
% 状态：[pos(3); vel(3); att(3); omega(3)] = 12维
x_true = zeros(12, N_uav, N_steps+1);     % 真实状态
x_est = zeros(12, N_uav, N_steps+1);      % 估计状态
u = zeros(4, N_uav, N_steps);             % 控制输入
disturbance_est = zeros(6, N_uav, N_steps); % 干扰估计
wind_forces = zeros(3, N_uav, N_steps);   % 风扰动力
wind_torques = zeros(3, N_uav, N_steps);  % 风扰动力矩

% 观测器状态
observer_states = cell(N_uav, 1);
for i = 1:N_uav
    observer_states{i} = [];
end

%% 初始条件设置
% 领导者初始位置
leader_init_pos = [0; 0; formation_height];

% 各无人机初始状态
for i = 1:N_uav
    if i == leader_idx
        % 领导者初始状态
        x_true(1:3, i, 1) = leader_init_pos;
        x_true(4:6, i, 1) = [0; 0; 0];        % 初始速度
        x_true(7:9, i, 1) = [0; 0; 0];        % 初始姿态角
        x_true(10:12, i, 1) = [0; 0; 0];      % 初始角速度
    else
        % 跟随者初始状态（在编队位置附近）
        follower_idx = i - 1;
        x_true(1:3, i, 1) = leader_init_pos + formation_offset(:, follower_idx) + 0.5*randn(3,1);
        x_true(4:6, i, 1) = 0.1*randn(3,1);   % 小的初始速度扰动
        x_true(7:9, i, 1) = 0.05*randn(3,1);  % 小的初始姿态扰动
        x_true(10:12, i, 1) = 0.02*randn(3,1); % 小的初始角速度扰动
    end
    
    % 初始估计状态（加入估计误差）
    x_est(:, i, 1) = x_true(:, i, 1) + 0.1*randn(12,1);
end

fprintf('初始条件设置完成\n');

%% 领导者轨迹生成
fprintf('生成领导者轨迹...\n');
leader_trajectory = zeros(12, N_steps+1);

for k = 1:N_steps+1
    t = (k-1) * dt;
    
    if t <= 5
        % 前5秒悬停稳定
        leader_trajectory(1:3, k) = leader_init_pos;
        leader_trajectory(4:6, k) = [0; 0; 0];
    else
        % 圆形轨迹飞行
        radius = 4.0;
        omega_traj = 0.3;
        t_flight = t - 5;
        
        % 位置轨迹
        leader_trajectory(1, k) = radius * cos(omega_traj * t_flight);
        leader_trajectory(2, k) = radius * sin(omega_traj * t_flight);
        leader_trajectory(3, k) = formation_height + 0.5 * sin(0.2 * t_flight);
        
        % 速度轨迹
        leader_trajectory(4, k) = -radius * omega_traj * sin(omega_traj * t_flight);
        leader_trajectory(5, k) = radius * omega_traj * cos(omega_traj * t_flight);
        leader_trajectory(6, k) = 0.5 * 0.2 * cos(0.2 * t_flight);
    end
    
    % 期望姿态和角速度（保持水平）
    leader_trajectory(7:9, k) = [0; 0; 0];
    leader_trajectory(10:12, k) = [0; 0; 0];
end

fprintf('领导者轨迹生成完成\n');

%% 主仿真循环
fprintf('开始仿真...\n');
tic;

for k = 1:N_steps
    t = k * dt;
    
    % 显示进度
    if mod(k, round(N_steps/10)) == 0
        fprintf('仿真进度: %.1f%%\n', k/N_steps*100);
    end
    
    %% 对每架无人机进行控制
    for i = 1:N_uav
        % 当前状态
        x_current = x_true(:, i, k);
        
        %% 风扰动计算
        pos = x_current(1:3);
        vel = x_current(4:6);
        att = x_current(7:9);
        
        [wind_force, wind_torque, ~] = WindDisturbanceModel(t, pos, vel, att, wind_params);
        wind_forces(:, i, k) = wind_force;
        wind_torques(:, i, k) = wind_torque;
        
        %% 干扰观测器
        % 测量输出（位置和姿态，加入测量噪声）
        y_pos = pos + 0.01*randn(3,1);
        y_att = att + 0.005*randn(3,1);
        y = [y_pos; y_att];
        
        % 上一步的控制输入
        if k > 1
            u_prev = u(:, i, k-1);
        else
            u_prev = [quad_params.mass * quad_params.gravity; 0; 0; 0];
        end
        
        % 干扰观测器
        [dist_est, observer_states{i}] = DisturbanceObserver(x_current, u_prev, y, ...
                                                           observer_states{i}, observer_params, dt);
        disturbance_est(:, i, k) = dist_est;
        
        %% 期望状态生成
        if i == leader_idx
            % 领导者跟踪预设轨迹
            x_desired = leader_trajectory(:, k);
        else
            % 跟随者跟踪领导者+编队偏移
            follower_idx = i - 1;
            leader_pos = x_true(1:3, leader_idx, k);
            leader_vel = x_true(4:6, leader_idx, k);
            
            x_desired = zeros(12, 1);
            x_desired(1:3) = leader_pos + formation_offset(:, follower_idx);
            x_desired(4:6) = leader_vel;
            x_desired(7:9) = [0; 0; 0];    % 期望姿态
            x_desired(10:12) = [0; 0; 0];  % 期望角速度
        end
        
        %% 鲁棒控制器
        if i == leader_idx
            controller_type = 'leader';
        else
            controller_type = 'follower';
        end
        [u_control, ~] = RobustController(x_current, x_desired, dist_est, ...
                                        controller_params, controller_type);
        u(:, i, k) = u_control;
        
        %% 四旋翼动力学更新
        % 系统动力学（包含风扰动）
        x_dot = QuadrotorDynamicsWithDisturbance(x_current, u_control, wind_force, wind_torque, quad_params);
        
        % 状态更新（欧拉积分）
        x_true(:, i, k+1) = x_current + dt * x_dot;
        
        %% 状态估计更新（简化的分布式观测器）
        % 这里使用简化的估计更新，实际应该包含邻居信息
        measurement_noise = 0.02 * randn(12, 1);
        x_est(:, i, k+1) = x_true(:, i, k+1) + measurement_noise;
    end
end

simulation_time = toc;
fprintf('仿真完成！用时: %.2f秒\n', simulation_time);

%% 结果可视化
fprintf('生成可视化结果...\n');
PlotRobustFormationResults(x_true, x_est, u, disturbance_est, wind_forces, wind_torques, ...
                          leader_trajectory, formation_offset, dt, T_sim);

fprintf('=== 仿真演示完成 ===\n');

end

%% 带干扰的四旋翼动力学函数
function x_dot = QuadrotorDynamicsWithDisturbance(x, u, wind_force, wind_torque, params)
% 带外部干扰的四旋翼动力学

m = params.mass;
g = params.gravity;
I = params.inertia;

% 状态提取
pos = x(1:3);
vel = x(4:6);
att = x(7:9);
omega = x(10:12);

% 控制输入
T = u(1);
tau = u(2:4);

% 旋转矩阵
phi = att(1); theta = att(2); psi = att(3);
R = [cos(theta)*cos(psi), sin(phi)*sin(theta)*cos(psi) - cos(phi)*sin(psi), cos(phi)*sin(theta)*cos(psi) + sin(phi)*sin(psi);
     cos(theta)*sin(psi), sin(phi)*sin(theta)*sin(psi) + cos(phi)*cos(psi), cos(phi)*sin(theta)*sin(psi) - sin(phi)*cos(psi);
     -sin(theta),         sin(phi)*cos(theta),                              cos(phi)*cos(theta)];

% 位置动力学
pos_dot = vel;

% 速度动力学（包含风扰动）
F_thrust = R * [0; 0; T];
F_gravity = [0; 0; -m*g];
F_total = F_thrust + F_gravity + wind_force;
vel_dot = F_total / m;

% 姿态动力学
W = [1, sin(phi)*tan(theta), cos(phi)*tan(theta);
     0, cos(phi),            -sin(phi);
     0, sin(phi)/cos(theta), cos(phi)/cos(theta)];
att_dot = W * omega;

% 角速度动力学（包含风扰动力矩）
omega_dot = I \ (tau + wind_torque - cross(omega, I * omega));

% 状态导数
x_dot = [pos_dot; vel_dot; att_dot; omega_dot];

end
