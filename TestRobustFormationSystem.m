function TestRobustFormationSystem()
% TestRobustFormationSystem - 测试完整的矩形编队控制系统
%
% 功能：验证基于分布式观测器的四旋翼矩形编队控制系统是否正常工作
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0

fprintf('=== 测试基于分布式观测器的四旋翼矩形编队控制系统 ===\n');

try
    % 运行主系统
    CompleteRectangularFormationSystem();
    
    fprintf('\n✅ 系统测试成功完成！\n');
    fprintf('所有功能模块正常工作：\n');
    fprintf('  ✓ 风扰动模型\n');
    fprintf('  ✓ 干扰观测器\n');
    fprintf('  ✓ 鲁棒控制器\n');
    fprintf('  ✓ 分布式观测器\n');
    fprintf('  ✓ 四旋翼动力学\n');
    fprintf('  ✓ 编队控制\n');
    fprintf('  ✓ 结果可视化\n');
    
catch ME
    fprintf('\n❌ 系统测试失败！\n');
    fprintf('错误信息：%s\n', ME.message);
    fprintf('错误位置：%s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    
    % 提供调试建议
    fprintf('\n调试建议：\n');
    fprintf('1. 检查MATLAB版本是否支持所有使用的函数\n');
    fprintf('2. 确保工作目录包含所有必要文件\n');
    fprintf('3. 检查系统内存是否足够\n');
    fprintf('4. 尝试减小仿真时间或增大仿真步长\n');
end

end
