# 基于分布式观测器的四旋翼矩形编队控制系统 - 完整版使用说明

## 系统概述

本系统是基于文档内容完整复现的四旋翼无人机矩形编队控制系统，集成了风扰动处理功能，解决了"缺少对外部干扰（如风扰）的显式处理"问题。

### 主要特点

- **领导者-跟随者架构**: 1个领导者 + 4个跟随者
- **矩形编队**: 跟随者位于矩形四个角，领导者在中心
- **分布式观测器**: 基于一致性算法的状态估计
- **完整6DOF动力学**: 四旋翼非线性动力学模型
- **风扰动处理**: 常值风、阵风、湍流等多种干扰类型
- **鲁棒控制**: PD控制+干扰补偿+滑模控制
- **中文可视化**: 完整的仿真结果可视化界面

## 文件结构

```
├── CompleteRectangularFormationSystem.m    # 主系统文件（包含所有功能）
├── TestRobustFormationSystem.m             # 系统测试文件
├── 完整系统使用说明.md                     # 本说明文档
└── 其他文档文件...
```

## 快速开始

### 方法1: 直接运行主系统
```matlab
CompleteRectangularFormationSystem
```

### 方法2: 运行测试脚本
```matlab
TestRobustFormationSystem
```

## 系统功能模块

### 1. 风扰动模型 (CalculateWindDisturbance)
- **常值风**: 恒定的背景风速
- **阵风**: 1-cosine阵风模型，模拟突发性强风
- **湍流**: 随机湍流扰动
- **风阻力计算**: 基于空气动力学的风阻力和力矩

### 2. 干扰观测器 (EstimateDisturbance)
- **实时估计**: 基于状态变化估计外部干扰
- **低通滤波**: 减少噪声影响
- **干扰限制**: 防止估计值过大

### 3. 鲁棒控制器
- **领导者控制器** (LeaderController): 轨迹跟踪 + 干扰补偿
- **跟随者控制器** (FollowerController): 编队保持 + 一致性控制 + 干扰补偿

### 4. 分布式观测器 (DistributedObserver)
- **状态估计**: 基于测量和邻居信息的状态估计
- **一致性项**: 通过邻居信息提高估计精度
- **测量更新**: 位置和姿态测量的融合

### 5. 四旋翼动力学 (QuadrotorDynamics)
- **6DOF模型**: 完整的位置、速度、姿态、角速度动力学
- **风扰动集成**: 外部干扰力和力矩的影响
- **数值稳定性**: 防止奇点和数值问题

## 系统参数

### 物理参数
- **质量**: 1.2 kg
- **转动惯量**: Ixx=Iyy=0.0347, Izz=0.0617 kg·m²
- **重力加速度**: 9.81 m/s²

### 控制参数
- **位置控制增益**: k_pos = 1.0
- **速度控制增益**: k_vel = 1.8
- **姿态控制增益**: k_att = 2.5
- **角速度控制增益**: k_omega = 1.2
- **编队保持增益**: k_formation = 0.5
- **一致性增益**: k_consensus = 0.2

### 风扰动参数
- **常值风速**: [2; 1; 0.5] m/s
- **阵风开始时间**: 10 s
- **阵风持续时间**: 3 s
- **阵风幅值**: 4 m/s
- **湍流强度**: 0.5 m/s

### 仿真参数
- **仿真时间**: 25秒
- **仿真步长**: 0.01秒
- **编队尺寸**: 2.0米
- **编队高度**: 5.0米

## 通信拓扑

系统采用混合通信拓扑：
```
领导者 ↔ 所有跟随者
跟随者1 ↔ 领导者、跟随者2、跟随者4
跟随者2 ↔ 领导者、跟随者1、跟随者3
跟随者3 ↔ 领导者、跟随者2、跟随者4
跟随者4 ↔ 领导者、跟随者1、跟随者3
```

## 可视化结果

系统会自动生成两个图表窗口：

### 图1: 编队控制分析
1. **3D轨迹图**: 显示所有无人机的飞行轨迹和编队连线
2. **编队形状演化**: 俯视图显示编队形状变化
3. **位置跟踪性能**: 各无人机的位置误差
4. **状态估计误差**: 分布式观测器性能

### 图2: 控制输入和干扰分析
1. **推力控制**: 各无人机的推力输入
2. **滚转/俯仰力矩**: 姿态控制力矩
3. **干扰观测器性能**: 估计干扰 vs 实际干扰
4. **风扰动分量**: 各方向的风扰动力
5. **编队保持误差**: 编队形状保持性能

## 性能指标

系统会自动计算并显示：
- **最终位置误差**: 各无人机相对期望位置的误差
- **平均状态估计误差**: 分布式观测器的估计精度
- **编队保持性能**: 编队形状保持的平均误差

### 预期性能表现
- 最终位置误差: < 1.0 m
- 状态估计误差: < 0.5 m
- 编队保持误差: < 2.0 m
- 收敛时间: < 10 秒

## 参数调优指南

### 提高抗干扰能力
```matlab
% 增加干扰观测器增益
disturbance_observer_gain = 20;

% 增加控制增益
k_pos = 1.5;
k_vel = 2.2;
```

### 提高编队精度
```matlab
% 增加编队保持增益
k_formation = 0.8;

% 增加一致性增益
k_consensus = 0.3;
```

### 提高系统稳定性
```matlab
% 减小控制增益
k_pos = 0.8;
k_vel = 1.5;

% 减小仿真步长
dt = 0.005;
```

## 扩展功能

### 修改编队形状
在主函数中修改 `formation_offset` 矩阵：
```matlab
% 三角形编队示例
formation_offset = [
     0,  formation_size, 0;           % 跟随者1: 前方
    -formation_size*sqrt(3)/2, -formation_size/2, 0;  % 跟随者2: 左后
     formation_size*sqrt(3)/2, -formation_size/2, 0;  % 跟随者3: 右后
     0, 0, formation_size             % 跟随者4: 上方
]';
```

### 修改风扰动类型
```matlab
% 关闭阵风，只保留常值风和湍流
wind_gust_amplitude = 0;

% 增加湍流强度
wind_turbulence_intensity = 1.0;
```

### 修改领导者轨迹
在轨迹生成部分修改：
```matlab
% 直线轨迹示例
leader_trajectory(1, k) = 0.5 * t_flight;  % x方向匀速
leader_trajectory(2, k) = 0;                % y方向保持
leader_trajectory(3, k) = formation_height; % 高度保持
```

## 故障排除

### 常见问题

1. **系统不稳定/发散**
   - 减小控制增益
   - 增大仿真步长
   - 检查初始条件

2. **编队误差过大**
   - 增加编队保持增益
   - 检查通信拓扑
   - 调整一致性增益

3. **干扰补偿效果差**
   - 增加干扰观测器增益
   - 检查风扰动参数设置
   - 调整滤波参数

4. **图表显示异常**
   - 检查MATLAB版本兼容性
   - 确保有足够内存
   - 重新运行系统

## 技术支持

如有问题或需要进一步定制，请检查：
1. MATLAB版本兼容性 (建议R2018b及以上)
2. 系统内存是否充足
3. 参数设置的合理性
4. 文件路径和依赖关系

---

**版本**: 1.0 - 集成风扰动处理  
**作者**: AI助手  
**日期**: 2025年7月3日  
**适用**: MATLAB R2018b及以上版本

## 创新点总结

相比原始系统，本版本的主要改进：

1. **✅ 解决了风扰动处理问题**
   - 集成了完整的风扰动模型
   - 实现了实时干扰观测器
   - 加入了干扰补偿控制

2. **✅ 增强了系统鲁棒性**
   - 多种风扰类型建模
   - 自适应干扰估计
   - 鲁棒控制策略

3. **✅ 保持了原有优势**
   - 分布式观测器架构
   - 矩形编队控制
   - 中文可视化界面

4. **✅ 提升了实用性**
   - 单文件集成设计
   - 参数可配置
   - 详细的性能分析
