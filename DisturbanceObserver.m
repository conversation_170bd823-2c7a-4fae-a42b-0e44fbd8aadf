function [disturbance_est, observer_state] = DisturbanceObserver(x, u, y, observer_state, observer_params, dt)
% DisturbanceObserver - 基于扩展状态观测器(ESO)的干扰观测器
%
% 功能：实时估计作用在四旋翼无人机上的外部干扰力和力矩
%
% 输入：
%   x              - 系统状态 [pos; vel; att; omega] (12×1)
%   u              - 控制输入 [T; tau_x; tau_y; tau_z] (4×1)
%   y              - 测量输出 (通常是位置和姿态)
%   observer_state - 观测器内部状态
%   observer_params - 观测器参数
%   dt             - 采样时间 (s)
%
% 输出：
%   disturbance_est - 估计的干扰 [F_dist; M_dist] (6×1)
%   observer_state  - 更新后的观测器状态
%
% 原理：
%   将干扰作为扩展状态，使用线性扩展状态观测器估计
%   系统模型：ẋ = f(x,u) + d(t)
%   扩展模型：[ẋ; ḋ] = [f(x,u) + d; 0] + [0; w]
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0

%% 参数检查和初始化
if nargin < 6
    dt = 0.01;  % 默认采样时间
end

if isempty(observer_params)
    observer_params = GetDefaultObserverParams();
end

% 系统参数
m = observer_params.mass;           % 质量 (kg)
g = observer_params.gravity;        % 重力加速度 (m/s²)
I = observer_params.inertia;        % 转动惯量矩阵 (3×3)

% 状态提取
pos = x(1:3);      % 位置 [x; y; z]
vel = x(4:6);      % 速度 [vx; vy; vz]
att = x(7:9);      % 姿态角 [phi; theta; psi]
omega = x(10:12);  % 角速度 [p; q; r]

% 控制输入提取
T = u(1);          % 总推力
tau = u(2:4);      % 力矩 [tau_x; tau_y; tau_z]

%% 观测器状态初始化
if isempty(observer_state) || ~isfield(observer_state, 'initialized')
    observer_state = InitializeObserverState(x, observer_params);
end

%% 位置子系统的干扰观测器
% 扩展状态：[pos; vel; F_dist/m]
% 其中 F_dist 是作用在质心的外部干扰力

% 提取位置观测器状态
z_pos = observer_state.position_observer;  % 扩展状态 (9×1)

% 位置观测器参数
L_pos = observer_params.position_observer_gain;  % 观测器增益矩阵 (9×3)

% 系统输出矩阵
C_pos = [eye(3), zeros(3,6)];  % 只测量位置

% 系统输入矩阵 (考虑推力的影响)
phi = att(1); theta = att(2); psi = att(3);
R_body_to_world = [
    cos(theta)*cos(psi), sin(phi)*sin(theta)*cos(psi) - cos(phi)*sin(psi), cos(phi)*sin(theta)*cos(psi) + sin(phi)*sin(psi);
    cos(theta)*sin(psi), sin(phi)*sin(theta)*sin(psi) + cos(phi)*cos(psi), cos(phi)*sin(theta)*sin(psi) - sin(phi)*cos(psi);
    -sin(theta),         sin(phi)*cos(theta),                              cos(phi)*cos(theta)
];

% 推力在世界坐标系下的分量
F_thrust_world = R_body_to_world * [0; 0; T];
F_gravity = [0; 0; -m*g];
F_total_known = F_thrust_world + F_gravity;

% 系统矩阵
A_pos = [
    zeros(3,3), eye(3),     zeros(3,3);
    zeros(3,3), zeros(3,3), eye(3);
    zeros(3,3), zeros(3,3), zeros(3,3)
];

B_pos = [
    zeros(3,1);
    F_total_known / m;
    zeros(3,1)
];

% 观测器方程：ż = Az + Bu + L(y - Cz)
y_pos = pos;  % 位置测量
innovation_pos = y_pos - C_pos * z_pos;

% 观测器状态更新
z_pos_dot = A_pos * z_pos + B_pos + L_pos * innovation_pos;
z_pos = z_pos + dt * z_pos_dot;

% 提取位置干扰估计
F_dist_est = m * z_pos(7:9);  % 干扰力估计

% 更新观测器状态
observer_state.position_observer = z_pos;

%% 姿态子系统的干扰观测器
% 扩展状态：[att; omega; M_dist]
% 其中 M_dist 是作用在无人机上的外部干扰力矩

% 提取姿态观测器状态
z_att = observer_state.attitude_observer;  % 扩展状态 (9×1)

% 姿态观测器参数
L_att = observer_params.attitude_observer_gain;  % 观测器增益矩阵 (9×3)

% 系统输出矩阵
C_att = [eye(3), zeros(3,6)];  % 只测量姿态角

% 姿态动力学线性化（小角度假设）
A_att = [
    zeros(3,3), eye(3),     zeros(3,3);
    zeros(3,3), zeros(3,3), eye(3);
    zeros(3,3), zeros(3,3), zeros(3,3)
];

% 控制力矩的影响
B_att = [
    zeros(3,1);
    I \ tau;
    zeros(3,1)
];

% 观测器方程
y_att = att;  % 姿态测量
innovation_att = y_att - C_att * z_att;

% 观测器状态更新
z_att_dot = A_att * z_att + B_att + L_att * innovation_att;
z_att = z_att + dt * z_att_dot;

% 提取姿态干扰估计
M_dist_est = z_att(7:9);  % 干扰力矩估计

% 更新观测器状态
observer_state.attitude_observer = z_att;

%% 干扰估计滤波和限制
% 低通滤波减少噪声
alpha_filter = observer_params.filter_coefficient;

if isfield(observer_state, 'F_dist_filtered')
    observer_state.F_dist_filtered = alpha_filter * observer_state.F_dist_filtered + ...
                                    (1 - alpha_filter) * F_dist_est;
    observer_state.M_dist_filtered = alpha_filter * observer_state.M_dist_filtered + ...
                                    (1 - alpha_filter) * M_dist_est;
else
    observer_state.F_dist_filtered = F_dist_est;
    observer_state.M_dist_filtered = M_dist_est;
end

% 干扰估计限制
F_max = observer_params.max_force_disturbance;
M_max = observer_params.max_torque_disturbance;

F_dist_limited = max(-F_max, min(F_max, observer_state.F_dist_filtered));
M_dist_limited = max(-M_max, min(M_max, observer_state.M_dist_filtered));

%% 输出干扰估计
disturbance_est = [F_dist_limited; M_dist_limited];

% 更新观测器状态
observer_state.F_dist_raw = F_dist_est;
observer_state.M_dist_raw = M_dist_est;
observer_state.F_dist_filtered = F_dist_limited;
observer_state.M_dist_filtered = M_dist_limited;
observer_state.innovation_pos = innovation_pos;
observer_state.innovation_att = innovation_att;
observer_state.initialized = true;

end

%% 观测器状态初始化函数
function observer_state = InitializeObserverState(x, params)
% 初始化观测器状态

observer_state = struct();

% 位置观测器状态初始化 [pos; vel; F_dist/m]
pos = x(1:3);
vel = x(4:6);
observer_state.position_observer = [pos; vel; zeros(3,1)];

% 姿态观测器状态初始化 [att; omega; M_dist]
att = x(7:9);
omega = x(10:12);
observer_state.attitude_observer = [att; omega; zeros(3,1)];

% 滤波状态初始化
observer_state.F_dist_filtered = zeros(3,1);
observer_state.M_dist_filtered = zeros(3,1);

observer_state.initialized = true;

end

%% 默认观测器参数函数
function params = GetDefaultObserverParams()
% 获取默认观测器参数

params = struct();

% 系统物理参数
params.mass = 1.2;                    % 质量 (kg)
params.gravity = 9.81;                % 重力加速度 (m/s²)
params.inertia = diag([0.0347, 0.0347, 0.0617]);  % 转动惯量 (kg·m²)

% 位置观测器增益 (调节观测器收敛速度和噪声抑制)
omega_pos = 20;  % 观测器带宽
params.position_observer_gain = [
    3*omega_pos*eye(3);      % 位置误差增益
    3*omega_pos^2*eye(3);    % 速度误差增益
    omega_pos^3*eye(3)       % 干扰估计增益
];

% 姿态观测器增益
omega_att = 15;  % 观测器带宽
params.attitude_observer_gain = [
    3*omega_att*eye(3);      % 姿态误差增益
    3*omega_att^2*eye(3);    % 角速度误差增益
    omega_att^3*eye(3)       % 干扰估计增益
];

% 滤波参数
params.filter_coefficient = 0.9;     % 低通滤波系数 (0-1)

% 干扰限制
params.max_force_disturbance = [10; 10; 10];    % 最大干扰力 (N)
params.max_torque_disturbance = [2; 2; 2];      % 最大干扰力矩 (N·m)

end
