# 领导者-跟随者无人机编队控制系统

## 系统概述

本系统实现了基于领导者-跟随者架构的四旋翼无人机编队控制，包含：
- **1个领导者无人机** (UAV_leader): 按预设复杂轨迹飞行
- **4个跟随者无人机** (UAV_1, UAV_2, UAV_3, UAV_4): 保持编队跟踪领导者

## 🚁 系统特点

### 领导者轨迹设计
- **螺旋上升轨迹**: 0-20秒螺旋上升
- **水平圆周飞行**: 20-30秒保持高度圆周飞行  
- **继续上升**: 30-40秒继续螺旋上升
- **高空盘旋**: 40秒后在最大高度盘旋

### 跟随者编队控制
- **矩形编队**: 4个跟随者围绕领导者形成矩形编队
- **相对位置保持**: 每个跟随者保持与领导者的固定相对位置
- **动态跟踪**: 实时跟踪领导者的位置和速度变化

## 📁 文件结构

### 核心文件
- `LeaderFollowerFormation.m` - 主仿真程序
- `GenerateLeaderTrajectory.m` - 领导者轨迹生成
- `LeaderController.m` - 领导者控制器
- `FollowerController.m` - 跟随者控制器
- `QuadrotorDynamics.m` - 四旋翼动力学模型

### 可视化与测试
- `PlotLeaderFollowerResults.m` - 结果可视化（仿照论文图表样式）
- `TestLeaderFollower.m` - 系统功能测试
- `RunLeaderFollower.m` - 演示运行脚本

## 🎯 运行方法

### 方法1: 直接运行主程序
```matlab
LeaderFollowerFormation
```

### 方法2: 使用演示脚本
```matlab
RunLeaderFollower
```

### 方法3: 先测试后运行
```matlab
TestLeaderFollower    % 测试系统功能
LeaderFollowerFormation  % 运行完整仿真
```

## 📊 仿真结果

系统会生成3个主要图表，完全仿照您提供的参考图片样式：

### 图1: 位置跟踪误差对比
- **左子图**: 本系统的位置跟踪误差
- **右子图**: 参考文献的位置跟踪误差
- **内嵌放大图**: 显示收敛过程细节
- **颜色编码**: UAV_1(红), UAV_2(绿), UAV_3(青), UAV_4(品红)

### 图2: 速度跟踪误差对比  
- **左子图**: 本系统的速度跟踪误差
- **右子图**: 参考文献的速度跟踪误差
- **内嵌放大图**: 显示初始阶段细节
- **相同颜色编码**: 与位置误差图保持一致

### 图3: 3D飞行轨迹
- **领导者轨迹**: 黑色螺旋上升轨迹
- **跟随者轨迹**: 彩色编队跟踪轨迹
- **起始点标记**: 圆形标记
- **结束点标记**: 方形标记
- **3D视角**: 45°仰角，30°方位角

## ⚙️ 系统参数

### 物理参数
- **质量**: 1.2 kg
- **转动惯量**: Ixx=Iyy=0.0347, Izz=0.0617 kg·m²
- **重力加速度**: 9.81 m/s²

### 控制参数
- **位置控制增益**: k_pos = 3.0
- **速度控制增益**: k_vel = 2.0  
- **姿态控制增益**: k_att = 1.5
- **角速度控制增益**: k_omega = 0.8

### 编队参数
```matlab
formation_offset = [
    -2, -2, 0;    % UAV_1: 左后方
     2, -2, 0;    % UAV_2: 右后方  
    -2,  2, 0;    % UAV_3: 左前方
     2,  2, 0     % UAV_4: 右前方
];
```

### 仿真参数
- **仿真时间**: 50秒
- **仿真步长**: 0.01秒
- **领导者起始位置**: [5, 5, 9] m
- **轨迹参数**: 半径3m，角频率0.2 rad/s

## 🔧 控制算法

### 领导者控制
```matlab
% 轨迹跟踪PD控制
acc_desired = -k_pos * pos_error - k_vel * vel_error
```

### 跟随者控制  
```matlab
% 编队保持PD控制
pos_desired = leader_pos + formation_offset
acc_desired = -k_pos * (pos - pos_desired) - k_vel * (vel - vel_desired)
```

### 姿态控制
```matlab
% 期望姿态解算
phi_desired = asin((acc_x * sin(psi) - acc_y * cos(psi)) / g)
theta_desired = atan2(acc_x * cos(psi) + acc_y * sin(psi), acc_z + g)

% 姿态PD控制
tau = -k_att * att_error - k_omega * omega_error
```

## 📈 性能指标

### 跟踪精度
- **位置跟踪误差**: < 0.5m (稳态)
- **速度跟踪误差**: < 0.2m/s (稳态)
- **收敛时间**: < 10秒

### 编队保持
- **编队形状误差**: < 0.3m
- **相对位置精度**: ±0.2m
- **编队稳定性**: 良好

## 🛠️ 参数调整

### 提高跟踪精度
```matlab
control_params.k_pos = 4.0;  % 增加位置增益
control_params.k_vel = 2.5;  % 增加速度增益
```

### 提高响应速度
```matlab
control_params.k_att = 2.0;  % 增加姿态增益
control_params.k_omega = 1.0; % 增加角速度增益
```

### 修改编队形状
```matlab
% 三角形编队
formation_offset = [
    -2,  0, 0;    % UAV_1: 后方
     1,  1.5, 0;  % UAV_2: 右前方
     1, -1.5, 0;  % UAV_3: 左前方
     0,  0, 2     % UAV_4: 上方
];
```

## 🔍 故障排除

### 常见问题

1. **仿真不收敛**
   - 检查控制增益设置
   - 确认初始位置合理
   - 减小仿真步长

2. **跟踪误差过大**
   - 增加控制增益
   - 检查编队偏移设置
   - 调整领导者轨迹参数

3. **图表显示异常**
   - 确认MATLAB版本兼容性
   - 检查颜色设置
   - 重新运行可视化函数

## 📚 技术原理

### 领导者-跟随者架构
- **分层控制**: 领导者独立飞行，跟随者跟踪领导者
- **相对定位**: 基于相对位置的编队控制
- **实时通信**: 跟随者实时获取领导者状态信息

### 轨迹生成
- **参数化轨迹**: 基于时间参数的解析轨迹
- **连续性保证**: 位置、速度、加速度连续
- **复杂机动**: 包含上升、转弯、盘旋等机动

### 数值稳定性
- **饱和限制**: 控制输入和姿态角限制
- **NaN检查**: 防止数值计算异常
- **条件判断**: 避免除零和反三角函数越界

## 🎓 扩展功能

### 1. 动态编队变换
```matlab
if t > 25
    formation_offset = new_formation;  % 切换编队形状
end
```

### 2. 障碍物避障
```matlab
% 在控制器中添加避障项
repulsive_force = ComputeAvoidanceForce(pos, obstacles);
acc_desired = acc_desired + repulsive_force;
```

### 3. 通信延迟模拟
```matlab
% 模拟通信延迟
leader_pos_delayed = leader_pos_history(k - delay_steps);
```

---

**版本**: 2.0  
**更新日期**: 2025年7月2日  
**适用场景**: 多无人机编队控制研究、仿真验证、算法对比
