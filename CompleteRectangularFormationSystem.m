function CompleteRectangularFormationSystem()
% CompleteRectangularFormationSystem - 完整的基于分布式观测器的四旋翼矩形编队控制系统
%
% 功能：实现1个领导者+4个跟随者的矩形编队控制，集成分布式观测器、
%       风扰动模型和鲁棒控制器
%
% 系统特点：
%   1. 领导者-跟随者架构：1个领导者 + 4个跟随者
%   2. 矩形编队：跟随者位于矩形四个角，领导者在中心
%   3. 分布式观测器：基于一致性算法的状态估计
%   4. 完整6DOF动力学：四旋翼非线性动力学模型
%   5. 风扰动处理：常值风、阵风、湍流等多种干扰
%   6. 鲁棒控制：PD控制+干扰补偿+滑模控制
%   7. 中文可视化：完整的仿真结果可视化界面
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0 - 集成干扰处理的完整版本

clear; clc; close all;

fprintf('=== 基于分布式观测器的四旋翼矩形编队控制系统 ===\n');
fprintf('版本：1.0 - 集成风扰动处理\n');
fprintf('正在初始化系统...\n');

%% ==================== 系统参数设置 ====================

% 仿真参数
dt = 0.01;              % 仿真步长 (s)
T_sim = 25;             % 仿真时间 (s)
N_steps = round(T_sim / dt);

% 无人机数量和编队配置
N_uav = 5;              % 1个领导者 + 4个跟随者
leader_idx = 1;         % 领导者索引

% 四旋翼物理参数
m = 1.2;                % 质量 (kg)
g = 9.81;               % 重力加速度 (m/s²)
I = diag([0.0347, 0.0347, 0.0617]);  % 转动惯量矩阵 (kg·m²)

% 编队参数
formation_size = 2.0;   % 编队尺寸 (m)
formation_height = 5.0; % 编队高度 (m)

% 矩形编队偏移（相对于领导者的位置）
formation_offset = [
     formation_size,  formation_size, 0;    % 跟随者1: 右前方
    -formation_size,  formation_size, 0;    % 跟随者2: 左前方
    -formation_size, -formation_size, 0;    % 跟随者3: 左后方
     formation_size, -formation_size, 0     % 跟随者4: 右后方
]';

% 控制参数（优化后的参数）
k_pos = 2.5;            % 位置控制增益（增加）
k_vel = 3.0;            % 速度控制增益（增加）
k_att = 4.0;            % 姿态控制增益（增加）
k_omega = 2.0;          % 角速度控制增益（增加）
k_formation = 1.2;      % 编队保持增益（增加）
k_consensus = 0.5;      % 一致性增益（增加）

% 观测器参数（优化后的参数）
observer_gain = 5.0;    % 观测器增益（增加）
consensus_gain = 0.8;   % 一致性观测器增益（增加）

% 风扰动参数
enable_wind = true;     % 是否启用风扰动
wind_constant = [2; 1; 0.5];           % 常值风速 (m/s)
wind_gust_start = 10;                  % 阵风开始时间 (s)
wind_gust_duration = 3;                % 阵风持续时间 (s)
wind_gust_amplitude = 4;               % 阵风幅值 (m/s)
wind_turbulence_intensity = 0.5;       % 湍流强度 (m/s)

% 干扰观测器参数
enable_disturbance_observer = true;    % 是否启用干扰观测器
disturbance_observer_gain = 15;        % 干扰观测器增益

fprintf('系统参数设置完成\n');

%% ==================== 通信拓扑矩阵 ====================
% 邻接矩阵：定义无人机之间的通信连接
% 1表示有通信连接，0表示无连接
A_comm = [
    0, 1, 1, 1, 1;  % 领导者与所有跟随者通信
    1, 0, 1, 0, 1;  % 跟随者1与领导者、跟随者2、4通信
    1, 1, 0, 1, 0;  % 跟随者2与领导者、跟随者1、3通信
    1, 0, 1, 0, 1;  % 跟随者3与领导者、跟随者2、4通信
    1, 1, 0, 1, 0   % 跟随者4与领导者、跟随者1、3通信
];

% 拉普拉斯矩阵
D_comm = diag(sum(A_comm, 2));  % 度矩阵
L_comm = D_comm - A_comm;       % 拉普拉斯矩阵

fprintf('通信拓扑设置完成\n');

%% ==================== 状态变量初始化 ====================
% 状态向量：[x, y, z, vx, vy, vz, φ, θ, ψ, p, q, r]' (12维)
x_true = zeros(12, N_uav, N_steps+1);      % 真实状态
x_est = zeros(12, N_uav, N_steps+1);       % 估计状态
u = zeros(4, N_uav, N_steps);              % 控制输入 [T, τx, τy, τz]
disturbance_est = zeros(6, N_uav, N_steps); % 干扰估计 [Fx, Fy, Fz, Mx, My, Mz]
wind_disturbance = zeros(6, N_uav, N_steps); % 实际风扰动

% 领导者轨迹
leader_trajectory = zeros(12, N_steps+1);

% 初始条件设置
leader_init_pos = [0; 0; formation_height];

for i = 1:N_uav
    if i == leader_idx
        % 领导者初始状态
        x_true(1:3, i, 1) = leader_init_pos;
        x_true(4:6, i, 1) = [0; 0; 0];        % 初始速度
        x_true(7:9, i, 1) = [0; 0; 0];        % 初始姿态角
        x_true(10:12, i, 1) = [0; 0; 0];      % 初始角速度
    else
        % 跟随者初始状态（在编队位置附近）
        follower_idx = i - 1;
        x_true(1:3, i, 1) = leader_init_pos + formation_offset(:, follower_idx) + 0.3*randn(3,1);
        x_true(4:6, i, 1) = 0.1*randn(3,1);   % 小的初始速度扰动
        x_true(7:9, i, 1) = 0.02*randn(3,1);  % 小的初始姿态扰动
        x_true(10:12, i, 1) = 0.01*randn(3,1); % 小的初始角速度扰动
    end
    
    % 初始估计状态（加入估计误差）
    x_est(:, i, 1) = x_true(:, i, 1) + 0.05*randn(12,1);
end

fprintf('状态变量初始化完成\n');

%% ==================== 领导者轨迹生成 ====================
fprintf('生成领导者轨迹...\n');

for k = 1:N_steps+1
    t = (k-1) * dt;
    
    if t <= 5
        % 前5秒悬停稳定
        leader_trajectory(1:3, k) = leader_init_pos;
        leader_trajectory(4:6, k) = [0; 0; 0];
    else
        % 圆形轨迹飞行
        radius = 3.0;
        omega_traj = 0.2;
        t_flight = t - 5;
        
        % 位置轨迹
        leader_trajectory(1, k) = radius * cos(omega_traj * t_flight);
        leader_trajectory(2, k) = radius * sin(omega_traj * t_flight);
        leader_trajectory(3, k) = formation_height + 0.3 * sin(0.15 * t_flight);
        
        % 速度轨迹
        leader_trajectory(4, k) = -radius * omega_traj * sin(omega_traj * t_flight);
        leader_trajectory(5, k) = radius * omega_traj * cos(omega_traj * t_flight);
        leader_trajectory(6, k) = 0.3 * 0.15 * cos(0.15 * t_flight);
    end
    
    % 期望姿态和角速度（保持水平）
    leader_trajectory(7:9, k) = [0; 0; 0];
    leader_trajectory(10:12, k) = [0; 0; 0];
end

fprintf('领导者轨迹生成完成\n');

%% ==================== 主仿真循环 ====================
fprintf('开始仿真...\n');
tic;

for k = 1:N_steps
    t = k * dt;
    
    % 显示进度
    if mod(k, round(N_steps/10)) == 0
        fprintf('仿真进度: %.1f%%\n', k/N_steps*100);
    end
    
    %% 对每架无人机进行控制和状态更新
    for i = 1:N_uav
        % 当前状态
        x_current = x_true(:, i, k);
        x_est_current = x_est(:, i, k);
        
        %% 风扰动计算
        if enable_wind
            wind_dist = CalculateWindDisturbance(t, x_current, wind_constant, ...
                wind_gust_start, wind_gust_duration, wind_gust_amplitude, wind_turbulence_intensity);
            wind_disturbance(:, i, k) = wind_dist;
        else
            wind_dist = zeros(6, 1);
            wind_disturbance(:, i, k) = wind_dist;
        end
        
        %% 干扰观测器
        if enable_disturbance_observer && k > 1
            % 使用前一步的控制输入和当前状态估计干扰
            u_prev = u(:, i, k-1);
            dist_est = EstimateDisturbance(x_current, u_prev, disturbance_observer_gain, dt);
            disturbance_est(:, i, k) = dist_est;
        else
            disturbance_est(:, i, k) = zeros(6, 1);
        end
        
        %% 期望状态生成
        if i == leader_idx
            % 领导者跟踪预设轨迹
            x_desired = leader_trajectory(:, k);
        else
            % 跟随者跟踪领导者+编队偏移
            follower_idx = i - 1;
            leader_pos = x_est(1:3, leader_idx, k);
            leader_vel = x_est(4:6, leader_idx, k);
            
            x_desired = zeros(12, 1);
            x_desired(1:3) = leader_pos + formation_offset(:, follower_idx);
            x_desired(4:6) = leader_vel;
            x_desired(7:9) = [0; 0; 0];    % 期望姿态
            x_desired(10:12) = [0; 0; 0];  % 期望角速度
        end
        
        %% 控制器计算
        if i == leader_idx
            % 领导者控制器
            u_control = LeaderController(x_est_current, x_desired, disturbance_est(:, i, k), ...
                k_pos, k_vel, k_att, k_omega, m, g, I);
        else
            % 跟随者控制器（包含编队保持和一致性项）
            u_control = FollowerController(x_est_current, x_desired, disturbance_est(:, i, k), ...
                x_est, i, A_comm, k_pos, k_vel, k_att, k_omega, k_formation, k_consensus, m, g, I);
        end
        
        u(:, i, k) = u_control;
        
        %% 四旋翼动力学更新
        x_dot = QuadrotorDynamics(x_current, u_control, wind_dist, m, g, I);
        x_true(:, i, k+1) = x_current + dt * x_dot;
        
        %% 分布式观测器更新
        x_est(:, i, k+1) = DistributedObserver(x_est_current, u_control, x_true(:, i, k+1), ...
            x_est, i, A_comm, observer_gain, consensus_gain, dt);
    end
end

simulation_time = toc;
fprintf('仿真完成！用时: %.2f秒\n', simulation_time);

%% ==================== 结果可视化 ====================
fprintf('生成可视化结果...\n');
PlotResults(x_true, x_est, u, disturbance_est, wind_disturbance, leader_trajectory, ...
    formation_offset, dt, T_sim, N_uav, leader_idx);

fprintf('=== 仿真演示完成 ===\n');

end

%% ==================== 子函数定义 ====================

function wind_dist = CalculateWindDisturbance(t, x, wind_constant, gust_start, gust_duration, gust_amplitude, turbulence_intensity)
% 计算风扰动力和力矩
% 输入：
%   t - 当前时间
%   x - 当前状态 [pos; vel; att; omega]
%   其他参数 - 风扰动参数
% 输出：
%   wind_dist - 风扰动 [Fx; Fy; Fz; Mx; My; Mz]

% pos = x(1:3);  % 位置（当前未使用）
vel = x(4:6);
% att = x(7:9);  % 姿态（当前未使用）

% 空气动力学参数
rho = 1.225;        % 空气密度 (kg/m³)
Cd = 1.2;           % 阻力系数
A_ref = 0.1;        % 参考面积 (m²)

% 初始化风扰动
wind_velocity = zeros(3, 1);
wind_dist = zeros(6, 1);

%% 1. 常值风
wind_velocity = wind_velocity + wind_constant;

%% 2. 阵风
if t >= gust_start && t <= gust_start + gust_duration
    % 1-cosine阵风模型
    tau = (t - gust_start) / gust_duration;
    gust_factor = 0.5 * (1 - cos(pi * tau));
    gust_wind = gust_amplitude * gust_factor * [1; 0.5; 0];
    wind_velocity = wind_velocity + gust_wind;
end

%% 3. 湍流
turbulence_wind = turbulence_intensity * randn(3, 1);
wind_velocity = wind_velocity + turbulence_wind;

%% 4. 计算风阻力
V_rel = wind_velocity - vel;
V_rel_mag = norm(V_rel);

if V_rel_mag > 0.01
    % 风阻力 F = 0.5 * rho * Cd * A * V_rel^2 * (V_rel/|V_rel|)
    F_wind = 0.5 * rho * Cd * A_ref * V_rel_mag * V_rel;

    % 风扰动力矩（由于压心偏移）
    cp_offset = [0; 0; 0.05];  % 压心偏移 (m)
    M_wind = 0.1 * cross(cp_offset, F_wind);  % 简化的力矩模型

    wind_dist = [F_wind; M_wind];
end

end

function dist_est = EstimateDisturbance(x, u, ~, dt)
% 简化的干扰观测器
% 输入：
%   x - 当前状态
%   u - 控制输入
%   observer_gain - 观测器增益
%   dt - 采样时间
% 输出：
%   dist_est - 估计的干扰

persistent x_prev u_prev dist_est_prev

if isempty(x_prev)
    x_prev = x;
    u_prev = u;
    dist_est_prev = zeros(6, 1);
end

% 简化的干扰估计（基于状态变化和控制输入的差异）
% pos = x(1:3);      % 当前位置（未使用）
vel = x(4:6);
% att = x(7:9);      % 当前姿态（未使用）
omega = x(10:12);

% pos_prev = x_prev(1:3);    % 前一时刻位置（未使用）
vel_prev = x_prev(4:6);
% att_prev = x_prev(7:9);    % 前一时刻姿态（未使用）
omega_prev = x_prev(10:12);

% 位置干扰估计
acc_measured = (vel - vel_prev) / dt;
acc_expected = [0; 0; -9.81];  % 简化：只考虑重力

F_dist_est = 1.2 * (acc_measured - acc_expected);  % 质量 * 加速度差异

% 姿态干扰估计
alpha_measured = (omega - omega_prev) / dt;
M_dist_est = 0.1 * alpha_measured;  % 简化的力矩估计

% 低通滤波
alpha_filter = 0.8;
dist_est = alpha_filter * dist_est_prev + (1 - alpha_filter) * [F_dist_est; M_dist_est];

% 限制干扰估计
max_force = 5.0;
max_torque = 1.0;
dist_est(1:3) = max(-max_force, min(max_force, dist_est(1:3)));
dist_est(4:6) = max(-max_torque, min(max_torque, dist_est(4:6)));

% 更新历史状态
x_prev = x;
u_prev = u;
dist_est_prev = dist_est;

end

function u = LeaderController(x, x_desired, disturbance_est, k_pos, k_vel, k_att, k_omega, m, g, I)
% 领导者控制器
% 输入：
%   x - 当前状态
%   x_desired - 期望状态
%   disturbance_est - 估计干扰
%   控制增益和系统参数
% 输出：
%   u - 控制输入 [T; tau_x; tau_y; tau_z]

% 状态提取
pos = x(1:3);
vel = x(4:6);
att = x(7:9);
omega = x(10:12);

pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 位置控制
e_pos = pos - pos_d;
e_vel = vel - vel_d;

% 期望加速度（PD控制 + 干扰补偿）
acc_desired = -k_pos * e_pos - k_vel * e_vel;

% 干扰补偿
if norm(disturbance_est(1:3)) > 0.1
    acc_desired = acc_desired - disturbance_est(1:3) / m;
end

% 期望推力和姿态
psi_d = att_d(3);
acc_total = acc_desired + [0; 0; g];
T = m * norm(acc_total);

% 期望姿态角
if T > 0.1 && norm(acc_total) > 0.1
    sin_phi_term = (acc_desired(1) * sin(psi_d) - acc_desired(2) * cos(psi_d)) / norm(acc_total);
    sin_phi_term = max(-0.99, min(0.99, sin_phi_term));  % 限制asin输入范围
    phi_d = asin(sin_phi_term);
    theta_d = atan2((acc_desired(1) * cos(psi_d) + acc_desired(2) * sin(psi_d)), acc_desired(3) + g);
else
    phi_d = 0;
    theta_d = 0;
end

% 限制姿态角
max_angle = pi/6;  % 30度
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

att_desired = [phi_d; theta_d; psi_d];

% 姿态控制
e_att = att - att_desired;
e_omega = omega - omega_d;

% 角度误差归一化
e_att = atan2(sin(e_att), cos(e_att));

% 期望角加速度（PD控制 + 干扰补偿）
alpha_desired = -k_att * e_att - k_omega * e_omega;

% 干扰补偿
if norm(disturbance_est(4:6)) > 0.01
    alpha_desired = alpha_desired - I \ disturbance_est(4:6);
end

% 控制力矩
tau = I * alpha_desired;

% 控制输入限制和数值检查
if isnan(T) || isinf(T)
    T = m * g;  % 悬停推力
end
T = max(0.5, min(20, T));

if any(isnan(tau)) || any(isinf(tau))
    tau = [0; 0; 0];  % 零力矩
end
tau = max(-2, min(2, tau));

u = [T; tau];

end

function u = FollowerController(x, x_desired, disturbance_est, x_est_all, i, A_comm, ...
    k_pos, k_vel, k_att, k_omega, k_formation, k_consensus, m, g, I)
% 跟随者控制器（包含编队保持和一致性项）
% 输入：
%   x - 当前状态
%   x_desired - 期望状态
%   disturbance_est - 估计干扰
%   x_est_all - 所有无人机的估计状态
%   i - 当前无人机索引
%   A_comm - 通信邻接矩阵
%   控制增益和系统参数
% 输出：
%   u - 控制输入

% 状态提取
pos = x(1:3);
vel = x(4:6);
att = x(7:9);
omega = x(10:12);

pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 基础位置控制
e_pos = pos - pos_d;
e_vel = vel - vel_d;

acc_desired = -k_pos * e_pos - k_vel * e_vel;

% 编队保持项
formation_term = zeros(3, 1);
consensus_term = zeros(3, 1);

% 获取当前时刻的所有状态
N_uav = size(x_est_all, 2);
for j = 1:N_uav
    if A_comm(i, j) == 1 && j ~= i  % 如果有通信连接
        neighbor_pos = x_est_all(1:3, j, end);
        % neighbor_vel = x_est_all(4:6, j, end);  % 邻居速度（当前未使用）

        % 编队保持项
        formation_term = formation_term + k_formation * (pos - neighbor_pos);

        % 一致性项
        consensus_term = consensus_term + k_consensus * ((pos - pos_d) - (neighbor_pos - x_desired(1:3)));
    end
end

acc_desired = acc_desired - formation_term - consensus_term;

% 干扰补偿
if norm(disturbance_est(1:3)) > 0.1
    acc_desired = acc_desired - disturbance_est(1:3) / m;
end

% 期望推力和姿态
psi_d = att_d(3);
acc_total = acc_desired + [0; 0; g];
T = m * norm(acc_total);

% 期望姿态角
if T > 0.1 && norm(acc_total) > 0.1
    sin_phi_term = (acc_desired(1) * sin(psi_d) - acc_desired(2) * cos(psi_d)) / norm(acc_total);
    sin_phi_term = max(-0.99, min(0.99, sin_phi_term));  % 限制asin输入范围
    phi_d = asin(sin_phi_term);
    theta_d = atan2((acc_desired(1) * cos(psi_d) + acc_desired(2) * sin(psi_d)), acc_desired(3) + g);
else
    phi_d = 0;
    theta_d = 0;
end

% 限制姿态角
max_angle = pi/6;
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

att_desired = [phi_d; theta_d; psi_d];

% 姿态控制
e_att = att - att_desired;
e_omega = omega - omega_d;
e_att = atan2(sin(e_att), cos(e_att));

% 姿态一致性项
att_consensus_term = zeros(3, 1);
for j = 1:N_uav
    if A_comm(i, j) == 1 && j ~= i
        neighbor_att = x_est_all(7:9, j, end);
        att_consensus_term = att_consensus_term + k_consensus * (att - neighbor_att);
    end
end

alpha_desired = -k_att * e_att - k_omega * e_omega - 0.5 * att_consensus_term;

% 干扰补偿
if norm(disturbance_est(4:6)) > 0.01
    alpha_desired = alpha_desired - I \ disturbance_est(4:6);
end

tau = I * alpha_desired;

% 控制输入限制和数值检查
if isnan(T) || isinf(T)
    T = m * g;  % 悬停推力
end
T = max(0.5, min(20, T));

if any(isnan(tau)) || any(isinf(tau))
    tau = [0; 0; 0];  % 零力矩
end
tau = max(-2, min(2, tau));

u = [T; tau];

end

function x_dot = QuadrotorDynamics(x, u, wind_disturbance, m, g, I)
% 四旋翼动力学模型（包含风扰动）
% 输入：
%   x - 状态 [pos; vel; att; omega]
%   u - 控制输入 [T; tau]
%   wind_disturbance - 风扰动 [F_wind; M_wind]
%   m, g, I - 系统参数
% 输出：
%   x_dot - 状态导数

% 状态提取
% pos = x(1:3);     % 位置（当前未使用）
vel = x(4:6);
att = x(7:9);
omega = x(10:12);

% 控制输入
T = u(1);
tau = u(2:4);

% 风扰动
F_wind = wind_disturbance(1:3);
M_wind = wind_disturbance(4:6);

% 姿态角
phi = att(1);
theta = att(2);
psi = att(3);

% 旋转矩阵（机体坐标系到世界坐标系）
R = [cos(theta)*cos(psi), sin(phi)*sin(theta)*cos(psi) - cos(phi)*sin(psi), cos(phi)*sin(theta)*cos(psi) + sin(phi)*sin(psi);
     cos(theta)*sin(psi), sin(phi)*sin(theta)*sin(psi) + cos(phi)*cos(psi), cos(phi)*sin(theta)*sin(psi) - sin(phi)*cos(psi);
     -sin(theta),         sin(phi)*cos(theta),                              cos(phi)*cos(theta)];

% 位置动力学
pos_dot = vel;

% 速度动力学
F_thrust = R * [0; 0; T];
F_gravity = [0; 0; -m*g];
F_total = F_thrust + F_gravity + F_wind;
vel_dot = F_total / m;

% 姿态动力学
W = [1, sin(phi)*tan(theta), cos(phi)*tan(theta);
     0, cos(phi),            -sin(phi);
     0, sin(phi)/cos(theta), cos(phi)/cos(theta)];

% 防止奇点
if abs(cos(theta)) < 0.1
    W(3, 2) = sin(phi) / 0.1;
    W(3, 3) = cos(phi) / 0.1;
end

att_dot = W * omega;

% 角速度动力学
omega_dot = I \ (tau + M_wind - cross(omega, I * omega));

% 状态导数
x_dot = [pos_dot; vel_dot; att_dot; omega_dot];

% 数值检查和限制
if any(isnan(x_dot)) || any(isinf(x_dot))
    x_dot = zeros(12, 1);  % 如果有NaN或Inf，设为零
end

% 限制状态导数的大小
max_vel = 10;      % 最大速度 (m/s)
max_acc = 20;      % 最大加速度 (m/s²)
max_omega = 5;     % 最大角速度 (rad/s)
max_alpha = 10;    % 最大角加速度 (rad/s²)

x_dot(1:3) = max(-max_vel, min(max_vel, x_dot(1:3)));      % 位置导数（速度）
x_dot(4:6) = max(-max_acc, min(max_acc, x_dot(4:6)));      % 速度导数（加速度）
x_dot(7:9) = max(-max_omega, min(max_omega, x_dot(7:9)));  % 姿态导数（角速度）
x_dot(10:12) = max(-max_alpha, min(max_alpha, x_dot(10:12))); % 角速度导数（角加速度）

end

function x_est_new = DistributedObserver(x_est, ~, x_true, x_est_all, i, A_comm, observer_gain, consensus_gain, dt)
% 分布式观测器
% 输入：
%   x_est - 当前估计状态
%   u - 控制输入
%   x_true - 真实状态（用于生成测量）
%   x_est_all - 所有无人机的估计状态
%   i - 当前无人机索引
%   A_comm - 通信邻接矩阵
%   observer_gain, consensus_gain - 观测器增益
%   dt - 采样时间
% 输出：
%   x_est_new - 更新后的估计状态

% 测量输出（位置和姿态，加入测量噪声）
y_pos = x_true(1:3) + 0.01*randn(3,1);
y_att = x_true(7:9) + 0.005*randn(3,1);

% 估计误差
e_pos = y_pos - x_est(1:3);
e_att = y_att - x_est(7:9);

% 基础观测器更新
pos_correction = observer_gain * e_pos;
att_correction = observer_gain * e_att;

% 一致性项
consensus_pos = zeros(3, 1);
consensus_vel = zeros(3, 1);
consensus_att = zeros(3, 1);
consensus_omega = zeros(3, 1);

N_uav = size(x_est_all, 2);
for j = 1:N_uav
    if A_comm(i, j) == 1 && j ~= i
        neighbor_est = x_est_all(:, j, end);

        consensus_pos = consensus_pos + consensus_gain * (x_est(1:3) - neighbor_est(1:3));
        consensus_vel = consensus_vel + consensus_gain * (x_est(4:6) - neighbor_est(4:6));
        consensus_att = consensus_att + consensus_gain * (x_est(7:9) - neighbor_est(7:9));
        consensus_omega = consensus_omega + consensus_gain * (x_est(10:12) - neighbor_est(10:12));
    end
end

% 状态预测（简化的动力学模型）
x_est_pred = x_est;
x_est_pred(1:3) = x_est(1:3) + dt * x_est(4:6);  % 位置积分
x_est_pred(4:6) = x_est(4:6);                     % 速度保持
x_est_pred(7:9) = x_est(7:9) + dt * x_est(10:12); % 姿态积分
x_est_pred(10:12) = x_est(10:12);                 % 角速度保持

% 观测器更新
x_est_new = x_est_pred;
x_est_new(1:3) = x_est_new(1:3) + dt * pos_correction - dt * consensus_pos;
x_est_new(4:6) = x_est_new(4:6) + dt * 0.5 * pos_correction - dt * consensus_vel;
x_est_new(7:9) = x_est_new(7:9) + dt * att_correction - dt * consensus_att;
x_est_new(10:12) = x_est_new(10:12) + dt * 0.5 * att_correction - dt * consensus_omega;

end

function PlotResults(x_true, x_est, u, disturbance_est, wind_disturbance, leader_trajectory, ...
    formation_offset, dt, T_sim, N_uav, leader_idx)
% 结果可视化函数
% 输入：
%   x_true - 真实状态历史
%   x_est - 估计状态历史
%   u - 控制输入历史
%   disturbance_est - 干扰估计历史
%   wind_disturbance - 实际风扰动历史
%   leader_trajectory - 领导者轨迹
%   formation_offset - 编队偏移
%   dt - 采样时间
%   T_sim - 仿真时间
%   N_uav - 无人机数量
%   leader_idx - 领导者索引

N_steps = size(x_true, 3) - 1;
t = 0:dt:T_sim;

% 颜色设置
colors = ['r', 'g', 'b', 'm', 'c'];
uav_names = {'领导者', '跟随者1', '跟随者2', '跟随者3', '跟随者4'};

%% 图1: 编队控制分析
figure('Name', '编队控制分析', 'Position', [100, 100, 1200, 800]);

% 3D轨迹图
subplot(2, 2, 1);
hold on; grid on;
for i = 1:N_uav
    pos_traj = squeeze(x_true(1:3, i, :));
    plot3(pos_traj(1, :), pos_traj(2, :), pos_traj(3, :), colors(i), 'LineWidth', 1.5);

    % 起始点和结束点
    plot3(pos_traj(1, 1), pos_traj(2, 1), pos_traj(3, 1), [colors(i) 'o'], 'MarkerSize', 8, 'MarkerFaceColor', colors(i));
    plot3(pos_traj(1, end), pos_traj(2, end), pos_traj(3, end), [colors(i) 's'], 'MarkerSize', 8, 'MarkerFaceColor', colors(i));
end

% 绘制编队连线（最终时刻）
final_positions = squeeze(x_true(1:3, :, end));
leader_pos = final_positions(:, leader_idx);
for i = 1:N_uav
    if i ~= leader_idx
        plot3([leader_pos(1), final_positions(1, i)], [leader_pos(2), final_positions(2, i)], ...
              [leader_pos(3), final_positions(3, i)], 'k--', 'LineWidth', 1);
    end
end

xlabel('X位置 (m)'); ylabel('Y位置 (m)'); zlabel('Z位置 (m)');
title('三维飞行轨迹');
legend(uav_names, 'Location', 'best');
view(45, 30);

% 编队形状演化（俯视图）
subplot(2, 2, 2);
hold on; grid on;
time_snapshots = [1, round(N_steps/4), round(N_steps/2), round(3*N_steps/4), N_steps];
for t_idx = 1:length(time_snapshots)
    k = time_snapshots(t_idx);
    positions = squeeze(x_true(1:2, :, k));

    % 绘制无人机位置
    for i = 1:N_uav
        if i == leader_idx
            plot(positions(1, i), positions(2, i), 'ko', 'MarkerSize', 6, 'MarkerFaceColor', 'k');
        else
            plot(positions(1, i), positions(2, i), [colors(i) 'o'], 'MarkerSize', 6, 'MarkerFaceColor', colors(i));
        end
    end

    % 绘制编队连线
    leader_pos_2d = positions(:, leader_idx);
    for i = 1:N_uav
        if i ~= leader_idx
            plot([leader_pos_2d(1), positions(1, i)], [leader_pos_2d(2), positions(2, i)], 'k-', 'LineWidth', 0.5);
        end
    end
end

xlabel('X位置 (m)'); ylabel('Y位置 (m)');
title('编队形状演化（俯视图）');
axis equal;

% 位置跟踪性能
subplot(2, 2, 3);
hold on; grid on;
for i = 1:N_uav
    pos_error = zeros(1, N_steps+1);
    for k = 1:N_steps+1
        if i == leader_idx
            pos_desired = leader_trajectory(1:3, k);
        else
            follower_idx = i - 1;
            leader_pos = x_true(1:3, leader_idx, k);
            pos_desired = leader_pos + formation_offset(:, follower_idx);
        end
        pos_error(k) = norm(x_true(1:3, i, k) - pos_desired);
    end
    plot(t, pos_error, colors(i), 'LineWidth', 1.5);
end
xlabel('时间 (s)'); ylabel('位置误差 (m)');
title('位置跟踪性能');
legend(uav_names, 'Location', 'best');

% 状态估计误差
subplot(2, 2, 4);
hold on; grid on;
for i = 1:N_uav
    est_error = zeros(1, N_steps+1);
    for k = 1:N_steps+1
        est_error(k) = norm(x_true(1:3, i, k) - x_est(1:3, i, k));
    end
    plot(t, est_error, colors(i), 'LineWidth', 1.5);
end
xlabel('时间 (s)'); ylabel('状态估计误差 (m)');
title('分布式观测器性能');
legend(uav_names, 'Location', 'best');

%% 图2: 控制输入和干扰分析
figure('Name', '控制输入和干扰分析', 'Position', [150, 150, 1200, 800]);

% 推力控制
subplot(2, 3, 1);
hold on; grid on;
for i = 1:N_uav
    thrust_history = squeeze(u(1, i, :));
    plot(t(1:end-1), thrust_history, colors(i), 'LineWidth', 1.5);
end
xlabel('时间 (s)'); ylabel('推力 (N)');
title('推力控制输入');
legend(uav_names, 'Location', 'best');

% 滚转力矩
subplot(2, 3, 2);
hold on; grid on;
for i = 1:N_uav
    torque_x_history = squeeze(u(2, i, :));
    plot(t(1:end-1), torque_x_history, colors(i), 'LineWidth', 1.5);
end
xlabel('时间 (s)'); ylabel('滚转力矩 (N·m)');
title('滚转控制力矩');
legend(uav_names, 'Location', 'best');

% 俯仰力矩
subplot(2, 3, 3);
hold on; grid on;
for i = 1:N_uav
    torque_y_history = squeeze(u(3, i, :));
    plot(t(1:end-1), torque_y_history, colors(i), 'LineWidth', 1.5);
end
xlabel('时间 (s)'); ylabel('俯仰力矩 (N·m)');
title('俯仰控制力矩');
legend(uav_names, 'Location', 'best');

% 干扰估计 vs 实际干扰（位置）
subplot(2, 3, 4);
hold on; grid on;
i_plot = 2;  % 选择跟随者1进行展示
dist_est_norm = zeros(1, N_steps);
dist_actual_norm = zeros(1, N_steps);
for k = 1:N_steps
    dist_est_norm(k) = norm(disturbance_est(1:3, i_plot, k));
    dist_actual_norm(k) = norm(wind_disturbance(1:3, i_plot, k));
end
plot(t(1:end-1), dist_est_norm, 'r-', 'LineWidth', 1.5);
plot(t(1:end-1), dist_actual_norm, 'b--', 'LineWidth', 1.5);
xlabel('时间 (s)'); ylabel('干扰力大小 (N)');
title(['干扰观测器性能 - ' uav_names{i_plot}]);
legend('估计干扰', '实际干扰', 'Location', 'best');

% 风扰动分量
subplot(2, 3, 5);
hold on; grid on;
wind_x = squeeze(wind_disturbance(1, i_plot, :));
wind_y = squeeze(wind_disturbance(2, i_plot, :));
wind_z = squeeze(wind_disturbance(3, i_plot, :));
plot(t(1:end-1), wind_x, 'r-', 'LineWidth', 1.5);
plot(t(1:end-1), wind_y, 'g-', 'LineWidth', 1.5);
plot(t(1:end-1), wind_z, 'b-', 'LineWidth', 1.5);
xlabel('时间 (s)'); ylabel('风扰动力 (N)');
title(['风扰动分量 - ' uav_names{i_plot}]);
legend('Fx', 'Fy', 'Fz', 'Location', 'best');

% 编队保持误差
subplot(2, 3, 6);
hold on; grid on;
formation_error = zeros(1, N_steps+1);
for k = 1:N_steps+1
    error_sum = 0;
    for i = 2:N_uav  % 只计算跟随者
        follower_idx = i - 1;
        leader_pos = x_true(1:3, leader_idx, k);
        desired_pos = leader_pos + formation_offset(:, follower_idx);
        actual_pos = x_true(1:3, i, k);
        error_sum = error_sum + norm(actual_pos - desired_pos);
    end
    formation_error(k) = error_sum / (N_uav - 1);
end
plot(t, formation_error, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('平均编队误差 (m)');
title('编队保持性能');

%% 性能统计
fprintf('\n=== 系统性能统计 ===\n');
fprintf('最终位置误差:\n');
for i = 1:N_uav
    if i == leader_idx
        pos_desired = leader_trajectory(1:3, end);
    else
        follower_idx = i - 1;
        leader_pos = x_true(1:3, leader_idx, end);
        pos_desired = leader_pos + formation_offset(:, follower_idx);
    end
    pos_actual = x_true(1:3, i, end);

    % 检查是否有NaN或Inf值
    if any(isnan(pos_actual)) || any(isinf(pos_actual)) || any(isnan(pos_desired)) || any(isinf(pos_desired))
        final_error = NaN;
        fprintf('  %s: 数值异常 (NaN/Inf)\n', uav_names{i});
    else
        final_error = norm(pos_actual - pos_desired);
        fprintf('  %s: %.3f m\n', uav_names{i}, final_error);
    end
end

fprintf('\n平均状态估计误差:\n');
for i = 1:N_uav
    avg_est_error = 0;
    valid_count = 0;
    for k = 1:N_steps+1
        pos_true = x_true(1:3, i, k);
        pos_est = x_est(1:3, i, k);

        % 检查数值有效性
        if ~any(isnan(pos_true)) && ~any(isinf(pos_true)) && ~any(isnan(pos_est)) && ~any(isinf(pos_est))
            avg_est_error = avg_est_error + norm(pos_true - pos_est);
            valid_count = valid_count + 1;
        end
    end

    if valid_count > 0
        avg_est_error = avg_est_error / valid_count;
        fprintf('  %s: %.3f m\n', uav_names{i}, avg_est_error);
    else
        fprintf('  %s: 数值异常 (NaN/Inf)\n', uav_names{i});
    end
end

fprintf('\n编队保持性能:\n');
fprintf('  平均编队误差: %.3f m\n', mean(formation_error));
fprintf('  最大编队误差: %.3f m\n', max(formation_error));

end
