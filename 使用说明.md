# 分布式观测器四旋翼编队控制系统使用说明

## 系统简介

本系统实现了基于分布式观测器的四旋翼无人机编队控制，具有以下特点：

- **分布式架构**: 每架无人机都有自己的观测器和控制器
- **一致性算法**: 通过邻居信息实现编队协调
- **状态估计**: 分布式观测器估计无人机状态
- **鲁棒控制**: 考虑测量噪声和模型不确定性

## 文件说明

### 核心文件
- `QuadrotorDynamics.m` - 四旋翼动力学模型
- `DistributedObserver.m` - 分布式状态观测器
- `DistributedController.m` - 分布式编队控制器
- `FormationControlMain.m` - 主仿真程序

### 辅助文件
- `PlotResults.m` - 结果可视化
- `ConfigParameters.m` - 参数配置
- `TestSystem.m` - 系统测试
- `RunFormationControl.m` - 演示脚本

## 快速开始

### 方法1: 直接运行主程序
```matlab
FormationControlMain
```

### 方法2: 运行演示脚本
```matlab
RunFormationControl
```

### 方法3: 先测试系统
```matlab
TestSystem  % 测试系统功能
FormationControlMain  % 运行完整仿真
```

## 系统参数

### 四旋翼参数
- 质量: 1.2 kg
- 转动惯量: Ixx=Iyy=0.0347, Izz=0.0617 kg·m²
- 重力加速度: 9.81 m/s²

### 控制参数
- 位置控制增益: k_pos = 2.0
- 速度控制增益: k_vel = 1.5
- 姿态控制增益: k_att = 1.0
- 角速度控制增益: k_omega = 0.5
- 一致性增益: k_consensus = 0.8

### 仿真参数
- 仿真时间: 20秒
- 仿真步长: 0.01秒
- 无人机数量: 4架
- 编队形状: 正方形 (边长2米)
- 编队高度: 5米

## 通信拓扑

默认通信拓扑：
```
无人机1 ↔ 无人机2, 无人机3
无人机2 ↔ 无人机1, 无人机3, 无人机4
无人机3 ↔ 无人机1, 无人机2, 无人机4
无人机4 ↔ 无人机2, 无人机3
```

## 结果分析

仿真完成后会生成5个图表：

1. **3D轨迹图**: 显示无人机飞行轨迹
2. **位置跟踪**: 真实vs估计vs期望位置
3. **估计误差**: 观测器性能分析
4. **控制输入**: 推力和力矩信号
5. **编队误差**: 编队形成效果

## 技术原理

### 1. 分布式观测器
观测器方程：
```
x̂̇ᵢ = Ax̂ᵢ + Buᵢ + L(yᵢ - Cx̂ᵢ) + Σⱼ∈Nᵢ kc(x̂ⱼ - x̂ᵢ)
```

### 2. 编队控制
位置控制：
```
upos = -kp(pi - pd) - kv(vi - vd) - kc Σⱼ∈Nᵢ [(pi-pd) - (pj-pd)]
```

姿态控制：
```
τ = -katt(θi - θd) - kω(ωi - ωd) - kc Σⱼ∈Nᵢ [(θi-θd) - (θj-θd)]
```

### 3. 四旋翼动力学
12维状态向量：[位置(3), 速度(3), 欧拉角(3), 角速度(3)]
4维控制输入：[总推力, 滚转力矩, 俯仰力矩, 偏航力矩]

## 参数调整

### 提高收敛速度
- 增加控制增益 (k_pos, k_vel, k_att, k_omega)
- 增加一致性增益 (k_consensus)
- 增加观测器增益

### 提高稳定性
- 减小控制增益
- 增加阻尼项
- 减小仿真步长

### 改变编队形状
修改 `FormationControlMain.m` 中的期望位置：
```matlab
% 三角形编队
x_desired(1:3, 1) = formation_center + [0; formation_size; 0];
x_desired(1:3, 2) = formation_center + [-formation_size*sqrt(3)/2; -formation_size/2; 0];
x_desired(1:3, 3) = formation_center + [formation_size*sqrt(3)/2; -formation_size/2; 0];
```

## 常见问题

### Q1: 仿真出现NaN值
**解决方案**:
- 检查控制增益是否过大
- 确认初始条件合理
- 减小仿真步长

### Q2: 编队不收敛
**解决方案**:
- 检查通信拓扑连通性
- 调整一致性增益
- 增加控制增益

### Q3: 控制输入饱和
**解决方案**:
- 调整推力/力矩限制
- 减小控制增益
- 检查期望轨迹合理性

## 扩展功能

### 1. 添加障碍物避障
在控制器中添加人工势场：
```matlab
% 障碍物排斥力
repulsive_force = ComputeRepulsiveForce(pos_i, obstacles);
acc_desired = acc_desired + repulsive_force;
```

### 2. 动态编队变换
实现编队形状的在线切换：
```matlab
if t > 10
    formation_type = 'triangle';  % 切换到三角形编队
end
```

### 3. 通信故障处理
模拟通信中断：
```matlab
if rand < 0.1  % 10%概率通信故障
    neighbors(i, :) = 0;  % 断开所有连接
end
```

## 性能指标

- **编队误差**: RMS位置误差 < 0.1m
- **估计精度**: 状态估计误差 < 5%
- **收敛时间**: < 10秒
- **控制能耗**: 推力变化平滑

## 参考文献

1. Olfati-Saber, R. (2006). Flocking for multi-agent dynamic systems
2. Ren, W., & Beard, R. W. (2008). Distributed consensus in multi-vehicle cooperative control
3. Beard, R. W., & McLain, T. W. (2012). Small unmanned aircraft: Theory and practice

---

**版本**: 1.0  
**更新日期**: 2025年7月2日  
**技术支持**: 如有问题请检查参数设置和MATLAB版本兼容性
