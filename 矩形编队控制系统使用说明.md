# 基于分布式观测器的四旋翼无人机矩形编队控制系统

## 系统概述

本项目实现了基于分布式观测器的四旋翼无人机矩形编队控制系统，采用领导者-跟随者方法，包含一个领导者和四个跟随者，形成矩形队形，领导者位于中心。

### 主要特点

- **领导者-跟随者架构**: 1个领导者 + 4个跟随者
- **矩形编队**: 跟随者位于矩形四个角，领导者在中心
- **分布式观测器**: 基于一致性算法的状态估计
- **完整6DOF动力学**: 四旋翼非线性动力学模型
- **中文可视化**: 完整的仿真结果可视化界面

## 文件结构

```
├── FinalRectangularFormationDemo.m          # 主演示程序 (推荐使用)
├── StableRectangularFormation.m             # 稳定版本 (悬停测试)
├── OptimizedRectangularFormation.m          # 优化版本
├── RectangularFormationWithDistributedObserver.m  # 原始版本
├── QuadrotorDynamicsRectangular.m           # 四旋翼动力学模型
├── DistributedObserverRectangular.m         # 分布式观测器
├── LeaderControllerRectangular.m            # 领导者控制器
├── FollowerControllerRectangular.m          # 跟随者控制器
├── PlotRectangularFormationResults.m       # 结果可视化
└── 矩形编队控制系统使用说明.md            # 本说明文档
```

## 快速开始

### 1. 运行主演示程序

在MATLAB命令窗口中运行：

```matlab
FinalRectangularFormationDemo
```

这将启动完整的仿真演示，包括：
- 25秒仿真时间
- 前5秒悬停稳定
- 后20秒圆形轨迹跟踪
- 完整的可视化结果

### 2. 运行稳定版本 (推荐调试)

```matlab
StableRectangularFormation
```

这个版本专注于系统稳定性，适合：
- 参数调试
- 稳定性验证
- 悬停性能测试

### 3. 自定义参数运行

可以直接修改各个.m文件中的参数，主要参数包括：

```matlab
% 仿真参数
dt = 0.01;              % 仿真步长
T_sim = 25;             % 仿真时间
formation_size = 2.0;   % 编队尺寸

% 控制参数
k_pos = 1.0;           % 位置控制增益
k_vel = 1.8;           % 速度控制增益
k_formation = 0.5;     % 编队保持增益
```

## 系统架构

### 1. 四旋翼动力学模型

- **状态向量**: [x, y, z, vx, vy, vz, φ, θ, ψ, p, q, r]ᵀ (12维)
- **控制输入**: [T, τₓ, τᵧ, τᵤ]ᵀ (推力和三轴力矩)
- **完整6DOF非线性动力学**

### 2. 分布式观测器

基于一致性算法的状态估计：

```
x̂̇ᵢ = Ax̂ᵢ + Buᵢ + L(yᵢ - Cx̂ᵢ) + kc∑ⱼ∈Nᵢ(x̂ⱼ - x̂ᵢ)
```

其中：
- A, B, C: 线性化系统矩阵
- L: 观测器增益矩阵
- kc: 一致性增益
- Nᵢ: 无人机i的邻居集合

### 3. 领导者-跟随者控制

**领导者控制器**:
- 轨迹跟踪控制
- PD位置控制 + 姿态控制

**跟随者控制器**:
- 基于领导者状态的编队控制
- 编队保持 + 相对位置控制

### 4. 矩形编队配置

```
跟随者2 ---- 跟随者1
    |    领导者    |
跟随者3 ---- 跟随者4
```

相对位置偏移：
- 跟随者1: [+1, +1, 0] (右前)
- 跟随者2: [-1, +1, 0] (左前)
- 跟随者3: [-1, -1, 0] (左后)
- 跟随者4: [+1, -1, 0] (右后)

## 通信拓扑

系统采用混合通信拓扑：
- 领导者与所有跟随者通信
- 跟随者之间部分连接 (环形连接)
- 确保整个网络连通性

## 可视化结果

仿真完成后会生成两个图表窗口：

### 图1: 编队控制分析
1. **3D轨迹图**: 显示所有无人机的飞行轨迹
2. **编队形状演化**: 俯视图显示编队形状变化
3. **位置跟踪性能**: 各无人机的位置误差
4. **状态估计误差**: 分布式观测器性能

### 图2: 控制输入分析
1. **推力控制**: 各无人机的推力输入
2. **滚转力矩**: 滚转控制力矩
3. **俯仰力矩**: 俯仰控制力矩
4. **偏航力矩**: 偏航控制力矩

## 性能指标

系统会自动计算并显示以下性能指标：

- **最终位置误差**: 各无人机相对期望位置的误差
- **平均状态估计误差**: 分布式观测器的估计精度
- **编队保持性能**: 编队形状保持的平均误差

### 典型性能表现

- 最终位置误差: 2-12 m
- 状态估计误差: 1-3 m
- 编队保持误差: 5-10 m

## 参数调优指南

### 1. 提高稳定性
- 降低位置控制增益 `k_pos`
- 增加速度控制增益 `k_vel`
- 减小一致性增益 `k_consensus`

### 2. 提高响应速度
- 适当增加控制增益
- 增加观测器增益
- 减小仿真步长 `dt`

### 3. 改善编队精度
- 增加编队保持增益 `k_formation`
- 优化通信拓扑
- 减少测量噪声

## 扩展功能

### 1. 修改编队形状

在主程序中修改 `formation_offset` 数组：

```matlab
% 三角形编队示例
formation_offset(:, 1) = [0; formation_size; 0];
formation_offset(:, 2) = [-formation_size*sqrt(3)/2; -formation_size/2; 0];
formation_offset(:, 3) = [formation_size*sqrt(3)/2; -formation_size/2; 0];
```

### 2. 修改轨迹

在 `leader_trajectory` 生成部分修改期望轨迹：

```matlab
% 直线轨迹示例
leader_trajectory(1, k) = 0.5 * t;  % x方向匀速运动
leader_trajectory(2, k) = 0;        % y方向保持
leader_trajectory(3, k) = formation_height;  % 高度保持
```

### 3. 添加干扰

在动力学更新中添加外部干扰：

```matlab
% 风扰动示例
wind_disturbance = [0.1*sin(t); 0.1*cos(t); 0];
x_true(:, i, k+1) = x_true(:, i, k+1) + dt * [zeros(3,1); wind_disturbance; zeros(6,1)];
```

## 故障排除

### 常见问题

1. **系统不稳定/发散**
   - 检查控制增益是否过大
   - 确认初始条件合理
   - 减小仿真步长

2. **编队误差过大**
   - 调整编队保持增益
   - 检查通信拓扑连通性
   - 优化观测器参数

3. **观测器性能差**
   - 调整观测器增益矩阵
   - 减少测量噪声
   - 增加一致性增益

### 调试建议

1. 先运行 `StableRectangularFormation.m` 验证基本稳定性
2. 逐步增加轨迹复杂度
3. 使用可视化结果分析系统行为
4. 根据性能统计调整参数

## 技术支持

如有问题或需要进一步定制，请检查：
1. MATLAB版本兼容性 (建议R2018b及以上)
2. 参数设置的合理性
3. 文件路径和依赖关系

---

**版本**: 1.0  
**作者**: AI助手  
**日期**: 2025年7月3日  
**适用**: MATLAB R2018b及以上版本
