function [u, controller_info] = RobustController(x, x_desired, disturbance_est, controller_params, controller_type)
% RobustController - 带干扰补偿的鲁棒四旋翼控制器
%
% 功能：设计鲁棒控制器，包含干扰补偿和自适应控制策略
%
% 输入：
%   x              - 当前状态 [pos; vel; att; omega] (12×1)
%   x_desired      - 期望状态 [pos_d; vel_d; att_d; omega_d] (12×1)
%   disturbance_est - 估计干扰 [F_dist; M_dist] (6×1)
%   controller_params - 控制器参数结构体
%   controller_type - 控制器类型 ('leader' 或 'follower')
%
% 输出：
%   u              - 控制输入 [T; tau_x; tau_y; tau_z] (4×1)
%   controller_info - 控制器信息结构体
%
% 控制策略：
%   1. 基础PD控制 + 干扰前馈补偿
%   2. 滑模控制增强鲁棒性
%   3. 自适应增益调节
%   4. 控制输入饱和处理
%
% 作者：AI助手
% 日期：2025年7月3日
% 版本：1.0

%% 参数检查和默认值
if nargin < 5
    controller_type = 'follower';
end

if nargin < 4 || isempty(controller_params)
    controller_params = GetDefaultControllerParams();
end

if nargin < 3
    disturbance_est = zeros(6, 1);
end

%% 系统参数
m = controller_params.mass;           % 质量 (kg)
g = controller_params.gravity;        % 重力加速度 (m/s²)
I = controller_params.inertia;        % 转动惯量矩阵 (3×3)

%% 状态提取
pos = x(1:3);          % 当前位置
vel = x(4:6);          % 当前速度
att = x(7:9);          % 当前姿态角 [phi; theta; psi]
omega = x(10:12);      % 当前角速度

pos_d = x_desired(1:3);    % 期望位置
vel_d = x_desired(4:6);    % 期望速度
att_d = x_desired(7:9);    % 期望姿态角
omega_d = x_desired(10:12); % 期望角速度

%% 干扰估计提取
F_dist_est = disturbance_est(1:3);  % 估计的干扰力
M_dist_est = disturbance_est(4:6);  % 估计的干扰力矩

%% 位置控制器设计
% 位置和速度误差
e_pos = pos - pos_d;
e_vel = vel - vel_d;

% 基础PD控制增益
k_pos = controller_params.k_pos;
k_vel = controller_params.k_vel;

% 自适应增益调节
if controller_params.enable_adaptive_gains
    % 基于误差大小调节增益
    pos_error_norm = norm(e_pos);
    vel_error_norm = norm(e_vel);
    
    % 自适应因子
    adaptive_factor_pos = 1 + controller_params.adaptive_gain_pos * pos_error_norm;
    adaptive_factor_vel = 1 + controller_params.adaptive_gain_vel * vel_error_norm;
    
    k_pos = k_pos * adaptive_factor_pos;
    k_vel = k_vel * adaptive_factor_vel;
end

% 期望加速度（基础PD控制）
acc_desired_basic = -k_pos * e_pos - k_vel * e_vel;

% 干扰前馈补偿
if controller_params.enable_disturbance_compensation
    acc_disturbance_comp = -F_dist_est / m;  % 干扰补偿加速度
else
    acc_disturbance_comp = zeros(3, 1);
end

% 滑模控制增强
if controller_params.enable_sliding_mode
    % 滑模面
    lambda = controller_params.sliding_mode_lambda;
    s_pos = e_vel + lambda * e_pos;
    
    % 滑模控制项
    eta = controller_params.sliding_mode_eta;
    delta = controller_params.sliding_mode_delta;
    
    % 连续滑模控制（避免抖振）
    acc_sliding = -eta * s_pos ./ (norm(s_pos) + delta);
else
    acc_sliding = zeros(3, 1);
end

% 总期望加速度
acc_desired = acc_desired_basic + acc_disturbance_comp + acc_sliding;

% 期望推力和姿态角计算
psi_d = att_d(3);  % 期望偏航角

% 总推力（包含重力补偿）
T_desired = m * (norm(acc_desired + [0; 0; g]));

% 期望姿态角计算
if T_desired > 0.1  % 避免除零
    phi_desired = asin((acc_desired(1) * sin(psi_d) - acc_desired(2) * cos(psi_d)) / ...
                      (norm(acc_desired + [0; 0; g])));
    theta_desired = atan2((acc_desired(1) * cos(psi_d) + acc_desired(2) * sin(psi_d)), ...
                         (acc_desired(3) + g));
else
    phi_desired = 0;
    theta_desired = 0;
end

% 限制姿态角
max_angle = controller_params.max_attitude_angle;
phi_desired = max(-max_angle, min(max_angle, phi_desired));
theta_desired = max(-max_angle, min(max_angle, theta_desired));

att_desired_computed = [phi_desired; theta_desired; psi_d];

%% 姿态控制器设计
% 姿态和角速度误差
e_att = att - att_desired_computed;
e_omega = omega - omega_d;

% 角度误差归一化到 [-π, π]
e_att = atan2(sin(e_att), cos(e_att));

% 基础PD控制增益
k_att = controller_params.k_att;
k_omega = controller_params.k_omega;

% 自适应增益调节
if controller_params.enable_adaptive_gains
    att_error_norm = norm(e_att);
    omega_error_norm = norm(e_omega);
    
    adaptive_factor_att = 1 + controller_params.adaptive_gain_att * att_error_norm;
    adaptive_factor_omega = 1 + controller_params.adaptive_gain_omega * omega_error_norm;
    
    k_att = k_att * adaptive_factor_att;
    k_omega = k_omega * adaptive_factor_omega;
end

% 期望角加速度（基础PD控制）
alpha_desired_basic = -k_att * e_att - k_omega * e_omega;

% 干扰前馈补偿
if controller_params.enable_disturbance_compensation
    alpha_disturbance_comp = -I \ M_dist_est;  % 干扰补偿角加速度
else
    alpha_disturbance_comp = zeros(3, 1);
end

% 滑模控制增强
if controller_params.enable_sliding_mode
    % 滑模面
    lambda_att = controller_params.sliding_mode_lambda_att;
    s_att = e_omega + lambda_att * e_att;
    
    % 滑模控制项
    eta_att = controller_params.sliding_mode_eta_att;
    delta_att = controller_params.sliding_mode_delta_att;
    
    % 连续滑模控制
    alpha_sliding = -eta_att * s_att ./ (norm(s_att) + delta_att);
else
    alpha_sliding = zeros(3, 1);
end

% 总期望角加速度
alpha_desired = alpha_desired_basic + alpha_disturbance_comp + alpha_sliding;

% 控制力矩
tau_desired = I * alpha_desired;

%% 控制输入饱和处理
% 推力限制
T_min = controller_params.thrust_min;
T_max = controller_params.thrust_max;
T = max(T_min, min(T_max, T_desired));

% 力矩限制
tau_max = controller_params.torque_max;
tau = max(-tau_max, min(tau_max, tau_desired));

%% 输出控制输入
u = [T; tau];

%% 控制器信息输出
controller_info = struct();
controller_info.position_error = e_pos;
controller_info.velocity_error = e_vel;
controller_info.attitude_error = e_att;
controller_info.angular_velocity_error = e_omega;
controller_info.desired_acceleration = acc_desired;
controller_info.desired_attitude = att_desired_computed;
controller_info.desired_angular_acceleration = alpha_desired;
controller_info.disturbance_compensation_acc = acc_disturbance_comp;
controller_info.disturbance_compensation_alpha = alpha_disturbance_comp;
controller_info.thrust_desired = T_desired;
controller_info.thrust_actual = T;
controller_info.torque_desired = tau_desired;
controller_info.torque_actual = tau;

if controller_params.enable_adaptive_gains
    controller_info.adaptive_gains = struct();
    controller_info.adaptive_gains.k_pos = k_pos;
    controller_info.adaptive_gains.k_vel = k_vel;
    controller_info.adaptive_gains.k_att = k_att;
    controller_info.adaptive_gains.k_omega = k_omega;
end

if controller_params.enable_sliding_mode
    controller_info.sliding_mode = struct();
    controller_info.sliding_mode.s_pos = s_pos;
    controller_info.sliding_mode.s_att = s_att;
    controller_info.sliding_mode.acc_sliding = acc_sliding;
    controller_info.sliding_mode.alpha_sliding = alpha_sliding;
end

end

%% 默认控制器参数函数
function params = GetDefaultControllerParams()
% 获取默认控制器参数

params = struct();

% 系统物理参数
params.mass = 1.2;                    % 质量 (kg)
params.gravity = 9.81;                % 重力加速度 (m/s²)
params.inertia = diag([0.0347, 0.0347, 0.0617]);  % 转动惯量 (kg·m²)

% 基础PD控制增益
params.k_pos = 2.0;                   % 位置控制增益
params.k_vel = 1.8;                   % 速度控制增益
params.k_att = 2.5;                   % 姿态控制增益
params.k_omega = 1.2;                 % 角速度控制增益

% 干扰补偿开关
params.enable_disturbance_compensation = true;

% 自适应增益开关和参数
params.enable_adaptive_gains = true;
params.adaptive_gain_pos = 0.5;       % 位置自适应增益系数
params.adaptive_gain_vel = 0.3;       % 速度自适应增益系数
params.adaptive_gain_att = 0.4;       % 姿态自适应增益系数
params.adaptive_gain_omega = 0.2;     % 角速度自适应增益系数

% 滑模控制开关和参数
params.enable_sliding_mode = true;
params.sliding_mode_lambda = 2.0;     % 位置滑模面参数
params.sliding_mode_eta = 1.0;        % 位置滑模增益
params.sliding_mode_delta = 0.1;      % 位置滑模边界层厚度
params.sliding_mode_lambda_att = 1.5; % 姿态滑模面参数
params.sliding_mode_eta_att = 0.8;    % 姿态滑模增益
params.sliding_mode_delta_att = 0.1;  % 姿态滑模边界层厚度

% 控制输入限制
params.thrust_min = 0.5;              % 最小推力 (N)
params.thrust_max = 20.0;             % 最大推力 (N)
params.torque_max = [2.0; 2.0; 1.0];  % 最大力矩 (N·m)
params.max_attitude_angle = pi/6;     % 最大姿态角 (30度)

end
